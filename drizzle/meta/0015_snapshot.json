{"id": "eea14d2f-5e40-441d-97c9-3afc8b7918a0", "prevId": "a899fd73-9636-4763-98e2-945d1e895ce7", "version": "7", "dialect": "postgresql", "tables": {"public.lists": {"name": "lists", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "space_id": {"name": "space_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"lists_user_id_users_id_fk": {"name": "lists_user_id_users_id_fk", "tableFrom": "lists", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "lists_space_id_spaces_id_fk": {"name": "lists_space_id_spaces_id_fk", "tableFrom": "lists", "tableTo": "spaces", "columnsFrom": ["space_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(\n      \"lists\".\"user_id\" = auth.user_id()\n      OR EXISTS (\n        SELECT 1 FROM space_collaborators sc\n        WHERE sc.space_id = \"lists\".\"space_id\"\n          AND sc.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(\n      \"lists\".\"user_id\" = auth.user_id()\n      OR EXISTS (\n        SELECT 1 FROM space_collaborators sc\n        WHERE sc.space_id = \"lists\".\"space_id\"\n          AND sc.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(\n      \"lists\".\"user_id\" = auth.user_id()\n      OR EXISTS (\n        SELECT 1 FROM space_collaborators sc\n        WHERE sc.space_id = \"lists\".\"space_id\"\n          AND sc.user_id = auth.user_id()\n      )\n    )", "withCheck": "(\n      \"lists\".\"user_id\" = auth.user_id()\n      OR EXISTS (\n        SELECT 1 FROM space_collaborators sc\n        WHERE sc.space_id = \"lists\".\"space_id\"\n          AND sc.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(\n      \"lists\".\"user_id\" = auth.user_id()\n      OR EXISTS (\n        SELECT 1 FROM space_collaborators sc\n        WHERE sc.space_id = \"lists\".\"space_id\"\n          AND sc.user_id = auth.user_id()\n      )\n    )"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.picklist_values": {"name": "picklist_values", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "tag_id": {"name": "tag_id", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"picklist_values_tag_id_tags_id_fk": {"name": "picklist_values_tag_id_tags_id_fk", "tableFrom": "picklist_values", "tableTo": "tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"picklist_values\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(\n      EXISTS (\n        SELECT 1 FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"picklist_values\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"picklist_values\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )", "withCheck": "(\n      EXISTS (\n        SELECT 1 FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"picklist_values\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"picklist_values\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.space_collaborators": {"name": "space_collaborators", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "space_id": {"name": "space_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"space_collaborators_space_id_spaces_id_fk": {"name": "space_collaborators_space_id_spaces_id_fk", "tableFrom": "space_collaborators", "tableTo": "spaces", "columnsFrom": ["space_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "space_collaborators_user_id_users_id_fk": {"name": "space_collaborators_user_id_users_id_fk", "tableFrom": "space_collaborators", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(\n      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = \"space_collaborators\".\"space_id\")\n      OR auth.user_id() = \"space_collaborators\".\"user_id\"\n      OR (SELECT auth.email()) = \"space_collaborators\".\"email\"\n    )"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(\n      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = \"space_collaborators\".\"space_id\")\n    )"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(\n      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = \"space_collaborators\".\"space_id\")\n    )", "withCheck": "(\n      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = \"space_collaborators\".\"space_id\")\n    )"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(\n      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = \"space_collaborators\".\"space_id\")\n    )"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.spaces": {"name": "spaces", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"spaces_user_id_users_id_fk": {"name": "spaces_user_id_users_id_fk", "tableFrom": "spaces", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(\n      auth.user_id() = \"spaces\".\"user_id\"\n      OR EXISTS (\n        SELECT 1 FROM space_collaborators sc\n        WHERE sc.space_id = \"spaces\".\"id\"\n          AND sc.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(\n      auth.user_id() = \"spaces\".\"user_id\"\n    )"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(\n      auth.user_id() = \"spaces\".\"user_id\"\n    )", "withCheck": "(\n      auth.user_id() = \"spaces\".\"user_id\"\n    )"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(\n      auth.user_id() = \"spaces\".\"user_id\"\n    )"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tags": {"name": "tags", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "space_id": {"name": "space_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "default": "'regular'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tags_user_id_users_id_fk": {"name": "tags_user_id_users_id_fk", "tableFrom": "tags", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tags_space_id_spaces_id_fk": {"name": "tags_space_id_spaces_id_fk", "tableFrom": "tags", "tableTo": "spaces", "columnsFrom": ["space_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM spaces s WHERE s.id = \"tags\".\"space_id\" AND s.user_id = auth.user_id()\n      ) OR EXISTS (\n        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = \"tags\".\"space_id\" AND sc.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(\n      EXISTS (\n        SELECT 1 FROM spaces s WHERE s.id = \"tags\".\"space_id\" AND s.user_id = auth.user_id()\n      ) OR EXISTS (\n        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = \"tags\".\"space_id\" AND sc.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM spaces s WHERE s.id = \"tags\".\"space_id\" AND s.user_id = auth.user_id()\n      ) OR EXISTS (\n        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = \"tags\".\"space_id\" AND sc.user_id = auth.user_id()\n      )\n    )", "withCheck": "(\n      EXISTS (\n        SELECT 1 FROM spaces s WHERE s.id = \"tags\".\"space_id\" AND s.user_id = auth.user_id()\n      ) OR EXISTS (\n        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = \"tags\".\"space_id\" AND sc.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM spaces s WHERE s.id = \"tags\".\"space_id\" AND s.user_id = auth.user_id()\n      ) OR EXISTS (\n        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = \"tags\".\"space_id\" AND sc.user_id = auth.user_id()\n      )\n    )"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_activities": {"name": "task_activities", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": true}, "activity_type": {"name": "activity_type", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"task_activities_user_id_users_id_fk": {"name": "task_activities_user_id_users_id_fk", "tableFrom": "task_activities", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_activities_task_id_tasks_id_fk": {"name": "task_activities_task_id_tasks_id_fk", "tableFrom": "task_activities", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.user_id() = \"task_activities\".\"user_id\")"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(select auth.user_id() = \"task_activities\".\"user_id\")"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(select auth.user_id() = \"task_activities\".\"user_id\")", "withCheck": "(select auth.user_id() = \"task_activities\".\"user_id\")"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(select auth.user_id() = \"task_activities\".\"user_id\")"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_picklist_selections": {"name": "task_picklist_selections", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "text", "primaryKey": false, "notNull": true}, "picklist_value_id": {"name": "picklist_value_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"task_picklist_selections_task_id_tasks_id_fk": {"name": "task_picklist_selections_task_id_tasks_id_fk", "tableFrom": "task_picklist_selections", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_picklist_selections_tag_id_tags_id_fk": {"name": "task_picklist_selections_tag_id_tags_id_fk", "tableFrom": "task_picklist_selections", "tableTo": "tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_picklist_selections_picklist_value_id_picklist_values_id_fk": {"name": "task_picklist_selections_picklist_value_id_picklist_values_id_fk", "tableFrom": "task_picklist_selections", "tableTo": "picklist_values", "columnsFrom": ["picklist_value_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1\n        FROM tasks t\n        JOIN lists l ON l.id = t.list_id\n        WHERE t.id = \"task_picklist_selections\".\"task_id\"\n          AND (\n            l.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(\n      EXISTS (\n        SELECT 1\n        FROM tasks t\n        JOIN lists l ON l.id = t.list_id\n        WHERE t.id = \"task_picklist_selections\".\"task_id\"\n          AND (\n            l.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n      AND EXISTS (\n        SELECT 1\n        FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"task_picklist_selections\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1\n        FROM tasks t\n        JOIN lists l ON l.id = t.list_id\n        WHERE t.id = \"task_picklist_selections\".\"task_id\"\n          AND (\n            l.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n      AND EXISTS (\n        SELECT 1\n        FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"task_picklist_selections\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )", "withCheck": "(\n      EXISTS (\n        SELECT 1\n        FROM tasks t\n        JOIN lists l ON l.id = t.list_id\n        WHERE t.id = \"task_picklist_selections\".\"task_id\"\n          AND (\n            l.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n      AND EXISTS (\n        SELECT 1\n        FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"task_picklist_selections\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1\n        FROM tasks t\n        JOIN lists l ON l.id = t.list_id\n        WHERE t.id = \"task_picklist_selections\".\"task_id\"\n          AND (\n            l.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n      AND EXISTS (\n        SELECT 1\n        FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"task_picklist_selections\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_tags": {"name": "task_tags", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"task_tags_task_id_tasks_id_fk": {"name": "task_tags_task_id_tasks_id_fk", "tableFrom": "task_tags", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_tags_tag_id_tags_id_fk": {"name": "task_tags_tag_id_tags_id_fk", "tableFrom": "task_tags", "tableTo": "tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1\n        FROM tasks t\n        JOIN lists l ON l.id = t.list_id\n        WHERE t.id = \"task_tags\".\"task_id\"\n          AND (\n            l.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(\n      EXISTS (\n        SELECT 1\n        FROM tasks t\n        JOIN lists l ON l.id = t.list_id\n        WHERE t.id = \"task_tags\".\"task_id\"\n          AND (\n            l.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n      AND EXISTS (\n        SELECT 1\n        FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"task_tags\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1\n        FROM tasks t\n        JOIN lists l ON l.id = t.list_id\n        WHERE t.id = \"task_tags\".\"task_id\"\n          AND (\n            l.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n      AND EXISTS (\n        SELECT 1\n        FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"task_tags\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )", "withCheck": "(\n      EXISTS (\n        SELECT 1\n        FROM tasks t\n        JOIN lists l ON l.id = t.list_id\n        WHERE t.id = \"task_tags\".\"task_id\"\n          AND (\n            l.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n      AND EXISTS (\n        SELECT 1\n        FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"task_tags\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1\n        FROM tasks t\n        JOIN lists l ON l.id = t.list_id\n        WHERE t.id = \"task_tags\".\"task_id\"\n          AND (\n            l.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n      AND EXISTS (\n        SELECT 1\n        FROM tags tg\n        JOIN spaces s ON s.id = tg.space_id\n        WHERE tg.id = \"task_tags\".\"tag_id\"\n          AND (\n            s.user_id = auth.user_id()\n            OR EXISTS (\n              SELECT 1 FROM space_collaborators sc\n              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()\n            )\n          )\n      )\n    )"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tasks": {"name": "tasks", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "assigned_user_id": {"name": "assigned_user_id", "type": "text", "primaryKey": false, "notNull": false}, "list_id": {"name": "list_id", "type": "text", "primaryKey": false, "notNull": true}, "parent_task_id": {"name": "parent_task_id", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'not_started'"}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"idx_tasks_assigned_user_id": {"name": "idx_tasks_assigned_user_id", "columns": [{"expression": "assigned_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tasks_user_id_users_id_fk": {"name": "tasks_user_id_users_id_fk", "tableFrom": "tasks", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tasks_assigned_user_id_users_id_fk": {"name": "tasks_assigned_user_id_users_id_fk", "tableFrom": "tasks", "tableTo": "users", "columnsFrom": ["assigned_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "tasks_list_id_lists_id_fk": {"name": "tasks_list_id_lists_id_fk", "tableFrom": "tasks", "tableTo": "lists", "columnsFrom": ["list_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(\n      \"tasks\".\"user_id\" = auth.user_id()\n      OR EXISTS (\n        SELECT 1\n        FROM lists l\n        JOIN space_collaborators sc ON sc.space_id = l.space_id\n        WHERE l.id = \"tasks\".\"list_id\"\n          AND sc.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(\n      \"tasks\".\"user_id\" = auth.user_id()\n      OR EXISTS (\n        SELECT 1\n        FROM lists l\n        JOIN space_collaborators sc ON sc.space_id = l.space_id\n        WHERE l.id = \"tasks\".\"list_id\"\n          AND sc.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(\n      \"tasks\".\"user_id\" = auth.user_id()\n      OR EXISTS (\n        SELECT 1\n        FROM lists l\n        JOIN space_collaborators sc ON sc.space_id = l.space_id\n        WHERE l.id = \"tasks\".\"list_id\"\n          AND sc.user_id = auth.user_id()\n      )\n    )", "withCheck": "(\n      \"tasks\".\"user_id\" = auth.user_id()\n      OR EXISTS (\n        SELECT 1\n        FROM lists l\n        JOIN space_collaborators sc ON sc.space_id = l.space_id\n        WHERE l.id = \"tasks\".\"list_id\"\n          AND sc.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(\n      \"tasks\".\"user_id\" = auth.user_id()\n      OR EXISTS (\n        SELECT 1\n        FROM lists l\n        JOIN space_collaborators sc ON sc.space_id = l.space_id\n        WHERE l.id = \"tasks\".\"list_id\"\n          AND sc.user_id = auth.user_id()\n      )\n    )"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_settings": {"name": "user_settings", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": true, "notNull": true}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": true, "default": "'system'"}, "notifications_enabled": {"name": "notifications_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "week_starts_on": {"name": "week_starts_on", "type": "text", "primaryKey": false, "notNull": true, "default": "'monday'"}, "mascot": {"name": "mascot", "type": "text", "primaryKey": false, "notNull": true, "default": "'golden'"}, "primary_color": {"name": "primary_color", "type": "text", "primaryKey": false, "notNull": false}, "default_space_id": {"name": "default_space_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_settings_user_id_users_id_fk": {"name": "user_settings_user_id_users_id_fk", "tableFrom": "user_settings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.user_id() = \"user_settings\".\"user_id\")"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(select auth.user_id() = \"user_settings\".\"user_id\")"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(select auth.user_id() = \"user_settings\".\"user_id\")", "withCheck": "(select auth.user_id() = \"user_settings\".\"user_id\")"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(select auth.user_id() = \"user_settings\".\"user_id\")"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.user_id() = \"users\".\"id\")"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(select auth.user_id() = \"users\".\"id\")"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(select auth.user_id() = \"users\".\"id\")", "withCheck": "(select auth.user_id() = \"users\".\"id\")"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(select auth.user_id() = \"users\".\"id\")"}}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}