-- Minimal migration for Task Assignee feature
-- Only includes objects missing from live DB, validated in temp branch

ALTER TABLE "tasks" ADD COLUMN "assigned_user_id" text;--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_assigned_user_id_users_id_fk" FOREIGN KEY ("assigned_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_tasks_assigned_user_id" ON "tasks" USING btree ("assigned_user_id");--> statement-breakpoint
