
## Gemini Project Documentation

This document provides a comprehensive overview of the NeoTask project, a task management application.

## Project Overview

NeoTask is a modern task management application built with Next.js 15, featuring:
- **Stack Auth** for authentication with custom sign-in/sign-up flows
- **Neon PostgreSQL** database with Drizzle ORM 
- **Multi-tenant architecture** with Spaces and collaborative features
- **Real-time task management** with drag-and-drop, status groups, and tagging
- **Mobile-first design** with PWA capabilities and swipe navigation
- **Advanced features** like bulk operations, inline editing, tag filtering, and smart caching

## Development Commands

```bash
# Development server with Turbopack
npm run dev

# Build for production
npm run build

# Build with bundle analyzer
npm run build:analyze

# Start production server
npm start

# Lint code
npm run lint

# Database operations
npm run db:generate    # Generate Drizzle migrations
npm run db:push       # Push schema changes to database  
npm run db:studio     # Open Drizzle Studio
```

## Architecture Overview

### Database Schema (src/lib/db/schema.ts)
- **Multi-tenant design**: Users → Spaces → Lists → Tasks
- **Collaborative features**: Space collaborators with email invites
- **Advanced tagging**: Regular tags and picklist tags with predefined values
- **Row Level Security**: Implemented with Drizzle policies using `crudPolicy`
- **Hierarchical tasks**: Parent-child task relationships with subtasks

### Authentication (Stack Auth)
- Server app configured in `src/stack.tsx`
- Custom auth pages in `src/app/handler/[...stack]/`
- Auth utilities in `src/lib/auth-utils.ts` and `src/lib/auth-sync.ts`
- Protected routes wrapped with `(protected)` route group

### State Management
- **TanStack Query** for server state with optimistic updates
- **React Context** for UI state (spaces, tag filters, list colors, themes)
- **Local state** for form management and UI interactions
- **Smart caching** with cache warming and persistence strategies

### Key Components Structure
```
src/
├── app/
│   ├── (protected)/           # Protected routes requiring auth
│   │   ├── tasks/             # Main task management interface
│   │   ├── dashboard/         # Activity dashboard
│   │   ├── calendar/          # Calendar view
│   │   └── settings/          # User settings
│   ├── handler/[...stack]/    # Stack Auth custom pages
│   └── api/                   # API routes
├── components/
│   ├── ui/                    # Reusable UI components (shadcn/ui)
│   └── spaces/                # Space-specific components
├── contexts/                  # React Context providers
├── hooks/                     # Custom React hooks
└── lib/
    ├── db/                    # Database schema and utilities
    ├── queries.ts             # TanStack Query hooks
    └── [various utilities]
```

## Key Development Patterns

### Database Operations
- Use Drizzle ORM with proper type inference
- All mutations should include optimistic updates via TanStack Query
- Respect RLS policies - operations are scoped to authenticated users
- Use batch operations for bulk actions

### Component Development
- Follow mobile-first responsive design
- Use shadcn/ui components as base, customize with CSS custom properties
- Implement proper loading states and error boundaries
- Support both touch and mouse interactions

### State Management Patterns
- Server state via TanStack Query with background refetching
- Local UI state with React hooks
- Context for cross-component shared state
- Optimistic updates for immediate feedback

### Testing Approach
- Component tests found in `src/components/ui/__tests__/`
- Uses `.test.tsx` and `.test.js` extensions
- Test database operations and UI interactions

## Code Conventions

### Styling
- Tailwind CSS with custom CSS variables for theming
- Support for light/dark modes via next-themes
- Dynamic colors based on list/tag selections
- Mobile-first responsive design patterns

### TypeScript
- Strict TypeScript configuration
- Drizzle schema types exported and used throughout
- Proper type inference for TanStack Query hooks

### File Organization
- Feature-based organization within route groups
- Shared components in `/components/ui/`
- Business logic in custom hooks and context providers
- API routes follow RESTful patterns

## Special Features

### Drag and Drop
- Uses `dnd-kit` with custom collision detection
- Supports cross-status group dragging
- Mobile touch-friendly with proper sensor configuration

### Offline Support
- Service worker for PWA functionality
- TanStack Query persistence for offline caching
- Background sync for data consistency

### Performance Optimizations
- Dynamic imports for heavy modal components
- Smart prefetching for list navigation
- Query deduplication and background updates
- Bundle splitting with Next.js automatic code splitting

### Accessibility
- Proper ARIA labels and keyboard navigation
- Screen reader support
- High contrast mode compatibility
- Touch target size compliance

## Important Notes

- Database migrations are handled via Drizzle Kit
- All user data is scoped by authentication state
- Color theming uses CSS custom properties for dynamic updates
- Mobile navigation uses swipe gestures with fallback controls
- Real-time updates handled via optimistic mutations rather than websockets