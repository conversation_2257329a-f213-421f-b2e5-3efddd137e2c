"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon, X, Clock } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { TimePicker } from "@/components/ui/time-picker";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { useMediaQuery } from "@/hooks/use-media-query";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import {
  formatDateTime,
  formatTime,
  hasTimeComponent,
  setTimeOnDate,
  clearTimeFromDate
} from "@/lib/date-utils";

export interface DateTimePickerProps {
  // Primary API
  date?: Date | undefined | null;
  setDate?: (date: Date | undefined | null) => void;

  // Alternative API for compatibility
  value?: Date | undefined | null;
  onChange?: (date: Date | undefined | null) => void;

  className?: string;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  includeTime?: boolean;
  clearable?: boolean;
}

export function DateTimePicker({
  date: dateProp,
  setDate: setDateProp,
  value: valueProp,
  onChange: onChangeProp,
  className,
  placeholder = "Pick a date",
  disabled = false,
  error,
  includeTime = true,
  clearable = false
}: DateTimePickerProps) {
  // Support both API styles for compatibility
  const date = dateProp ?? valueProp;
  const setDate = setDateProp ?? onChangeProp;

  // Validate that we have the required props
  if (!setDate) {
    throw new Error("DateTimePicker requires either setDate or onChange prop");
  }
  const [isDatePickerOpen, setIsDatePickerOpen] = React.useState(false);
  const [isTimePickerOpen, setIsTimePickerOpen] = React.useState(false);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Generate unique component ID for accessibility
  const componentId = React.useId();

  // Memoize time value to prevent unnecessary re-renders
  // TimePicker expects HH:MM format (24-hour), not formatted time string
  const timeValue = React.useMemo(() => {
    if (date && hasTimeComponent(date)) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    }
    return "";
  }, [date]);

  // Handle calendar date selection
  const handleCalendarSelect = React.useCallback((selectedDate: Date | undefined) => {
    if (selectedDate) {
      // If we have an existing date with time, preserve the time
      if (date && hasTimeComponent(date)) {
        const newDate = setTimeOnDate(selectedDate, date.getHours(), date.getMinutes());
        setDate(newDate);
      } else {
        setDate(selectedDate);
      }
    } else {
      setDate(selectedDate);
    }

    setIsDatePickerOpen(false);
  }, [setDate, date]);

  // Handle clear date from X button
  const handleClearDate = React.useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDate(null);
  }, [setDate]);

  // Handle clear time only
  const handleClearTime = React.useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (date) {
      const newDate = clearTimeFromDate(date);
      setDate(newDate);
    }
  }, [date, setDate]);

  // Handle today button
  const handleToday = React.useCallback(() => {
    const today = new Date();
    setDate(today);
    setIsDatePickerOpen(false);
  }, [setDate]);

  // Handle time pill click
  const handleTimePillClick = React.useCallback(() => {
    if (!disabled) {
      setIsTimePickerOpen(true);
    }
  }, [disabled]);

  // Shared date trigger component for both desktop and mobile
  const DateTrigger = () => (
    <>
      {date ? (
        // Date selected - style to match selected time
        <div
          className={cn(
            "inline-flex items-center gap-1.5 rounded-full px-2.5 py-1 text-xs font-medium transition-colors",
            "bg-muted/30 text-muted-foreground hover:bg-muted/50",
            "cursor-pointer",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          role="button"
          tabIndex={disabled ? -1 : 0}
          aria-label={`Selected date: ${format(date, "MMM d, yyyy")}. Click to change date.`}
          aria-describedby={error ? `datetime-input-error-${componentId}` : undefined}
          onClick={() => !disabled && setIsDatePickerOpen(true)}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              if (!disabled) setIsDatePickerOpen(true);
            }
          }}
        >
          <CalendarIcon className="h-3 w-3" />
          <span>
            {format(date, "MMM d, yyyy")}
          </span>

          {/* Clear date button */}
          <button
            type="button"
            className="px-1 py-0 m-0 border-0 bg-transparent hover:bg-destructive hover:text-destructive-foreground cursor-pointer shrink-0 flex items-center justify-center h-full rounded-full"
            onClick={handleClearDate}
            aria-label="Clear date"
          >
            <X className="h-3 w-3" />
          </button>
        </div>
      ) : (
        // No date selected - style like "Add time..." option
        <div
          className={cn(
            "inline-flex items-center gap-1.5 rounded-full border px-2.5 py-1 text-xs transition-colors",
            "text-muted-foreground hover:text-foreground",
            "cursor-pointer",
            error && "border-destructive",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          role="button"
          tabIndex={disabled ? -1 : 0}
          aria-label={placeholder}
          aria-describedby={error ? `datetime-input-error-${componentId}` : undefined}
          onClick={() => !disabled && setIsDatePickerOpen(true)}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              if (!disabled) setIsDatePickerOpen(true);
            }
          }}
        >
          <CalendarIcon className="h-3 w-3" />
          <span>{placeholder}</span>
        </div>
      )}
    </>
  );

  // Desktop calendar content (for popover)
  const DesktopCalendarContent = () => (
    <>
      <Calendar
        mode="single"
        selected={date || undefined}
        onSelect={handleCalendarSelect}
        disabled={disabled}
        initialFocus
        key={date ? date.toISOString() : `no-date-${Math.random()}`}
      />

      {/* Action buttons */}
      <div className="flex items-center justify-end p-3 border-t">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleToday}
          className="text-muted-foreground hover:text-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2"
        >
          Today
        </Button>
      </div>
    </>
  );

  // Mobile calendar content (for modal with centering)
  const MobileCalendarContent = () => (
    <div className="flex flex-col items-center w-full">
      <Calendar
        mode="single"
        selected={date || undefined}
        onSelect={handleCalendarSelect}
        disabled={disabled}
        initialFocus
        className="mx-auto"
        key={date ? date.toISOString() : `no-date-${Math.random()}`}
      />

      {/* Action buttons */}
      <div className="flex items-center justify-center w-full p-3 border-t">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleToday}
          className="text-muted-foreground hover:text-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2"
        >
          Today
        </Button>
      </div>
    </div>
  );

  return (
    <div className={cn("space-y-1", className)}>
      {/* Date and Time Picker Section - Horizontal Layout */}
      <div className="flex items-center gap-1">
        {/* Date Picker */}
        <div className="relative">
        {isDesktop ? (
          // Desktop: Use existing popover interface
          <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
            <PopoverTrigger asChild>
              {date ? (
                // Date selected - style to match selected time
                <div
                  className={cn(
                    "inline-flex items-center gap-1.5 rounded-full px-2.5 py-1 text-xs font-medium transition-colors",
                    "bg-muted/30 text-muted-foreground hover:bg-muted/50",
                    "cursor-pointer",
                    disabled && "opacity-50 cursor-not-allowed"
                  )}
                  role="button"
                  tabIndex={disabled ? -1 : 0}
                  aria-label={`Selected date: ${format(date, "MMM d, yyyy")}. Click to change date.`}
                  aria-describedby={error ? `datetime-input-error-${componentId}` : undefined}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      if (!disabled) setIsDatePickerOpen(true);
                    }
                  }}
                >
                  <CalendarIcon className="h-3 w-3" />
                  <span>
                    {format(date, "MMM d, yyyy")}
                  </span>

                  {/* Clear date button */}
                  <button
                    type="button"
                    className="px-1 py-0 m-0 border-0 bg-transparent hover:bg-destructive hover:text-destructive-foreground cursor-pointer shrink-0 flex items-center justify-center h-full rounded-full"
                    onClick={handleClearDate}
                    aria-label="Clear date"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ) : (
                // No date selected - style like "Add time..." option
                <div
                  className={cn(
                    "inline-flex items-center gap-1.5 rounded-full border px-2.5 py-1 text-xs transition-colors",
                    "text-muted-foreground hover:text-foreground",
                    "cursor-pointer",
                    error && "border-destructive",
                    disabled && "opacity-50 cursor-not-allowed"
                  )}
                  role="button"
                  tabIndex={disabled ? -1 : 0}
                  aria-label={placeholder}
                  aria-describedby={error ? `datetime-input-error-${componentId}` : undefined}
                  onClick={() => !disabled && setIsDatePickerOpen(true)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      if (!disabled) setIsDatePickerOpen(true);
                    }
                  }}
                >
                  <CalendarIcon className="h-3 w-3" />
                  <span>{placeholder}</span>
                </div>
              )}
            </PopoverTrigger>

            <PopoverContent
              className="w-auto p-0"
              align="start"
              sideOffset={4}
              collisionPadding={8}
            >
              <DesktopCalendarContent />
            </PopoverContent>
          </Popover>
        ) : (
          // Mobile: Use modal-style interface
          <>
            <DateTrigger />
            <MobileDialog open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
              <MobileDialogContent className="p-0">
                <MobileDialogHeader className="sr-only">
                  <VisuallyHidden>
                    <MobileDialogTitle>Select Date</MobileDialogTitle>
                  </VisuallyHidden>
                </MobileDialogHeader>
                {/* Center and constrain the calendar */}
                <div className="flex justify-center px-4 py-2">
                  <div className="w-full max-w-sm">
                    <MobileCalendarContent />
                  </div>
                </div>
              </MobileDialogContent>
            </MobileDialog>
          </>
        )}
        </div>

        {/* Time Picker - Only show when date is selected and includeTime is true */}
        {includeTime && date && (
          <div className="relative">
          {hasTimeComponent(date) ? (
            // Time is set - show time pill
            isDesktop ? (
              <Popover open={isTimePickerOpen} onOpenChange={setIsTimePickerOpen}>
                <PopoverTrigger asChild>
                  <div
                    className={cn(
                      "inline-flex items-center gap-1.5 rounded-full px-2.5 py-1 text-xs font-medium transition-colors",
                      "bg-muted/30 text-muted-foreground hover:bg-muted/50",
                      "cursor-pointer",
                      disabled && "opacity-50 cursor-not-allowed"
                    )}
                    role="button"
                    tabIndex={disabled ? -1 : 0}
                    aria-label={`Selected time: ${formatTime(date)}. Click to change time.`}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        if (!disabled) setIsTimePickerOpen(true);
                      }
                    }}
                  >
                    <Clock className="h-3 w-3" />
                    <span>
                      {formatTime(date)}
                    </span>

                    {/* Clear time button */}
                    <button
                      type="button"
                      className="px-1 py-0 m-0 border-0 bg-transparent hover:bg-destructive hover:text-destructive-foreground cursor-pointer shrink-0 flex items-center justify-center h-full rounded-full"
                      onClick={handleClearTime}
                      aria-label="Clear time"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                </PopoverTrigger>

                <PopoverContent
                  className="w-auto p-3"
                  align="start"
                  sideOffset={4}
                  collisionPadding={8}
                >
                  <TimePicker
                    value={timeValue}
                    showActions
                    onConfirm={(newTime) => {
                      if (date && newTime) {
                        const [hours, minutes] = newTime.split(':').map(Number);
                        if (!isNaN(hours) && !isNaN(minutes) && hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
                          const newDate = setTimeOnDate(date, hours, minutes);
                          setDate(newDate);
                        }
                      }
                      setIsTimePickerOpen(false);
                    }}
                    onCancel={() => {
                      setIsTimePickerOpen(false);
                    }}
                    onChange={() => {}}
                    disabled={disabled}
                    use12Hour={true}
                  />
                </PopoverContent>
              </Popover>
            ) : (
              // Mobile: Direct time picker with pill display
              <>
                <div
                  className={cn(
                    "inline-flex items-center gap-1.5 rounded-full px-2.5 py-1 text-xs font-medium transition-colors",
                    "bg-muted/30 text-muted-foreground hover:bg-muted/50",
                    "cursor-pointer",
                    disabled && "opacity-50 cursor-not-allowed"
                  )}
                  role="button"
                  tabIndex={disabled ? -1 : 0}
                  aria-label={`Selected time: ${formatTime(date)}. Click to change time.`}
                  onClick={() => !disabled && setIsTimePickerOpen(true)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      if (!disabled) setIsTimePickerOpen(true);
                    }
                  }}
                >
                  <Clock className="h-3 w-3" />
                  <span>
                    {formatTime(date)}
                  </span>

                  {/* Clear time button */}
                  <button
                    type="button"
                    className="px-1 py-0 m-0 border-0 bg-transparent hover:bg-destructive hover:text-destructive-foreground cursor-pointer shrink-0 flex items-center justify-center h-full rounded-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClearTime(e);
                    }}
                    aria-label="Clear time"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
                <TimePicker
                  value={timeValue}
                  showActions
                  onConfirm={(newTime) => {
                    if (date && newTime) {
                      const [hours, minutes] = newTime.split(':').map(Number);
                      if (!isNaN(hours) && !isNaN(minutes) && hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
                        const newDate = setTimeOnDate(date, hours, minutes);
                        setDate(newDate);
                      }
                    }
                    setIsTimePickerOpen(false);
                  }}
                  onCancel={() => setIsTimePickerOpen(false)}
                  onChange={() => {}}
                  disabled={disabled}
                  use12Hour={true}
                  className="sr-only" // Hide visually but keep for modal functionality
                  open={isTimePickerOpen}
                  onOpenChange={setIsTimePickerOpen}
                />
              </>
            )
          ) : (
            // No time set - show "Add time..." placeholder pill
            isDesktop ? (
              <Popover open={isTimePickerOpen} onOpenChange={setIsTimePickerOpen}>
                <PopoverTrigger asChild>
                  <div
                    className={cn(
                      "inline-flex items-center gap-1.5 rounded-full border px-2.5 py-1 text-xs transition-colors",
                      "text-muted-foreground hover:text-foreground",
                      "cursor-pointer",
                      disabled && "opacity-50 cursor-not-allowed"
                    )}
                    role="button"
                    tabIndex={disabled ? -1 : 0}
                    aria-label="Add time to this date"
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        if (!disabled) setIsTimePickerOpen(true);
                      }
                    }}
                  >
                    <Clock className="h-3 w-3" />
                    <span>Add time...</span>
                  </div>
                </PopoverTrigger>

                <PopoverContent
                  className="w-auto p-3"
                  align="start"
                  sideOffset={4}
                  collisionPadding={8}
                >
                  <TimePicker
                    value=""
                    showActions
                    onConfirm={(newTime) => {
                      if (date && newTime) {
                        const [hours, minutes] = newTime.split(':').map(Number);
                        if (!isNaN(hours) && !isNaN(minutes) && hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
                          const newDate = setTimeOnDate(date, hours, minutes);
                          setDate(newDate);
                        }
                      }
                      setIsTimePickerOpen(false);
                    }}
                    onCancel={() => {
                      setIsTimePickerOpen(false);
                    }}
                    onChange={() => {}}
                    disabled={disabled}
                    use12Hour={true}
                  />
                </PopoverContent>
              </Popover>
            ) : (
              // Mobile: Direct time picker with placeholder pill
              <>
                <div
                  className={cn(
                    "inline-flex items-center gap-1.5 rounded-full border px-2.5 py-1 text-xs transition-colors",
                    "text-muted-foreground hover:text-foreground",
                    "cursor-pointer",
                    disabled && "opacity-50 cursor-not-allowed"
                  )}
                  role="button"
                  tabIndex={disabled ? -1 : 0}
                  aria-label="Add time to this date"
                  onClick={() => !disabled && setIsTimePickerOpen(true)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      if (!disabled) setIsTimePickerOpen(true);
                    }
                  }}
                >
                  <Clock className="h-3 w-3" />
                  <span>Add time...</span>
                </div>
                <TimePicker
                  value=""
                  onChange={(newTime) => {
                    if (date && newTime) {
                      const [hours, minutes] = newTime.split(':').map(Number);
                      if (!isNaN(hours) && !isNaN(minutes) && hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
                        const newDate = setTimeOnDate(date, hours, minutes);
                        setDate(newDate);
                        setIsTimePickerOpen(false);
                      }
                    }
                  }}
                  disabled={disabled}
                  use12Hour={true}
                  className="sr-only" // Hide visually but keep for modal functionality
                  open={isTimePickerOpen}
                  onOpenChange={setIsTimePickerOpen}
                />
              </>
            )
          )}
        </div>
        )}
      </div>

      {/* External error prop */}
      {error && (
        <p
          className="text-xs text-destructive mt-1"
          id={`datetime-input-error-${componentId}`}
          role="alert"
        >
          {error}
        </p>
      )}
    </div>
  );
}
