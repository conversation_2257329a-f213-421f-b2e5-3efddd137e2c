"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { useMediaQuery } from "@/hooks/use-media-query";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

export interface DatePickerProps {
  date: Date | undefined | null;
  setDate: (date: Date | undefined | null) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  label?: string;
  error?: string;
}

export function DatePicker({
  date,
  setDate,
  className,
  placeholder = "Pick a date",
  disabled = false,
  error
}: DatePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Handle calendar date selection
  const handleCalendarSelect = React.useCallback((selectedDate: Date | undefined) => {
    setDate(selectedDate);
    setIsOpen(false);
  }, [setDate]);

  // Handle clear date from X button
  const handleClearDate = React.useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // Clear the date value directly without closing popover
    // Use null instead of undefined to ensure it survives JSON serialization
    setDate(null);
  }, [setDate]);

  // Handle today button
  const handleToday = React.useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDate(new Date());
    setIsOpen(false);
  }, [setDate]);

  // Generate unique IDs for accessibility
  const componentId = React.useId();
  const inputId = `date-input-${componentId}`;

  // Shared trigger component for both desktop and mobile
  const DateTrigger = () => (
    <>
      {date ? (
        // Date selected - show as pill/badge with X button
        <div
          className={cn(
            "inline-flex items-center gap-2 rounded-full border px-3 py-1.5 text-sm font-medium transition-colors",
            "bg-muted text-muted-foreground hover:bg-muted/80",
            "cursor-pointer",
            error && "border-destructive",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          role="button"
          tabIndex={disabled ? -1 : 0}
          aria-label={`Selected date: ${format(date, "MMM d, yyyy")}. Click to change date.`}
          aria-describedby={error ? `date-input-error-${componentId}` : undefined}
          onClick={() => !disabled && setIsOpen(true)}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              if (!disabled) setIsOpen(true);
            }
          }}
        >
          <CalendarIcon className="h-3.5 w-3.5" />
          <span>{format(date, "MMM d, yyyy")}</span>
          <button
            type="button"
            className="px-1 py-0 m-0 border-0 bg-transparent hover:bg-destructive hover:text-destructive-foreground cursor-pointer shrink-0 flex items-center justify-center h-full rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleClearDate}
            disabled={disabled}
            aria-label="Clear date"
          >
            <X className="h-3 w-3" />
          </button>
        </div>
      ) : (
        // No date selected - show as button
        <Button
          id={inputId}
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal text-muted-foreground",
            error && "border-destructive"
          )}
          disabled={disabled}
          aria-invalid={!!error}
          aria-describedby={error ? `date-input-error-${componentId}` : undefined}
          onClick={() => !disabled && setIsOpen(true)}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          <span>{placeholder}</span>
        </Button>
      )}
    </>
  );

  // Desktop calendar content (for popover)
  const DesktopCalendarContent = () => (
    <>
      <Calendar
        mode="single"
        selected={date || undefined}
        onSelect={handleCalendarSelect}
        disabled={disabled}
        initialFocus
        key={date ? date.toISOString() : `no-date-${Math.random()}`}
      />

      {/* Action buttons */}
      <div className="flex items-center justify-end p-3 border-t">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleToday}
          className="text-muted-foreground hover:text-foreground"
        >
          Today
        </Button>
      </div>
    </>
  );

  // Mobile calendar content (for modal with centering)
  const MobileCalendarContent = () => (
    <div className="flex flex-col items-center w-full">
      <Calendar
        mode="single"
        selected={date || undefined}
        onSelect={handleCalendarSelect}
        disabled={disabled}
        initialFocus
        className="mx-auto"
        key={date ? date.toISOString() : `no-date-${Math.random()}`}
      />

      {/* Action buttons */}
      <div className="flex items-center justify-center w-full p-3 border-t">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleToday}
          className="text-muted-foreground hover:text-foreground"
        >
          Today
        </Button>
      </div>
    </div>
  );

  return (
    <div className={cn("space-y-2", className)}>
      <div className="relative">
        {/* Screen reader only label - no visible labels per user preference */}
        <Label htmlFor={inputId} className="sr-only">
          Date input
        </Label>

        {isDesktop ? (
          // Desktop: Use existing popover interface
          <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
              {date ? (
                // Date selected - show as pill/badge with X button
                <div
                  className={cn(
                    "inline-flex items-center gap-2 rounded-full border px-3 py-1.5 text-sm font-medium transition-colors",
                    "bg-muted text-muted-foreground hover:bg-muted/80",
                    "cursor-pointer",
                    error && "border-destructive",
                    disabled && "opacity-50 cursor-not-allowed"
                  )}
                  role="button"
                  tabIndex={disabled ? -1 : 0}
                  aria-label={`Selected date: ${format(date, "MMM d, yyyy")}. Click to change date.`}
                  aria-describedby={error ? `date-input-error-${componentId}` : undefined}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      if (!disabled) setIsOpen(true);
                    }
                  }}
                >
                  <CalendarIcon className="h-3.5 w-3.5" />
                  <span>{format(date, "MMM d, yyyy")}</span>
                  <button
                    type="button"
                    className="px-1 py-0 m-0 border-0 bg-transparent hover:bg-destructive hover:text-destructive-foreground cursor-pointer shrink-0 flex items-center justify-center h-full rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={handleClearDate}
                    disabled={disabled}
                    aria-label="Clear date"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ) : (
                // No date selected - show as button
                <Button
                  id={inputId}
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal text-muted-foreground",
                    error && "border-destructive"
                  )}
                  disabled={disabled}
                  aria-invalid={!!error}
                  aria-describedby={error ? `date-input-error-${componentId}` : undefined}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  <span>{placeholder}</span>
                </Button>
              )}
            </PopoverTrigger>

            <PopoverContent className="w-auto p-0" align="start">
              <DesktopCalendarContent />
            </PopoverContent>
          </Popover>
        ) : (
          // Mobile: Use modal-style interface
          <>
            <DateTrigger />
            <MobileDialog open={isOpen} onOpenChange={setIsOpen}>
              <MobileDialogContent className="p-0">
                <MobileDialogHeader className="sr-only">
                  <VisuallyHidden>
                    <MobileDialogTitle>Select Date</MobileDialogTitle>
                  </VisuallyHidden>
                </MobileDialogHeader>
                {/* Center and constrain the calendar */}
                <div className="flex justify-center px-4 py-2">
                  <div className="w-full max-w-sm">
                    <MobileCalendarContent />
                  </div>
                </div>
              </MobileDialogContent>
            </MobileDialog>
          </>
        )}

        {/* External error prop */}
        {error && (
          <p
            className="text-xs text-destructive mt-1"
            id={`date-input-error-${componentId}`}
            role="alert"
          >
            {error}
          </p>
        )}
      </div>
    </div>
  );
}
