/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EnhancedRichTextEditor } from '../enhanced-rich-text-editor';

// Mock the markdown utilities
jest.mock('@/lib/markdown-utils', () => ({
  markdownToHtml: jest.fn((markdown: string) => `<p>${markdown}</p>`),
  htmlToMarkdown: jest.fn((html: string) => html.replace(/<[^>]*>/g, '')),
  normalizeMarkdown: jest.fn((markdown: string) => markdown.trim()),
  isMarkdown: jest.fn(() => false),
}));

// Mock Tiptap editor
jest.mock('@tiptap/react', () => ({
  useEditor: jest.fn(() => ({
    getHTML: jest.fn(() => '<p>test content</p>'),
    getText: jest.fn(() => 'test content'),
    commands: {
      setContent: jest.fn(),
      insertContentAt: jest.fn(),
      setTextSelection: jest.fn(),
      focus: jest.fn(),
      toggleBold: jest.fn(),
      toggleItalic: jest.fn(),
      toggleStrike: jest.fn(),
      toggleCode: jest.fn(),
      toggleHeading: jest.fn(),
      toggleBulletList: jest.fn(),
      toggleOrderedList: jest.fn(),
      toggleCodeBlock: jest.fn(),
      setLink: jest.fn(),
      unsetLink: jest.fn(),
      sinkListItem: jest.fn(),
      liftListItem: jest.fn(),
    },
    chain: jest.fn(() => ({
      focus: jest.fn(() => ({
        toggleBold: jest.fn(() => ({ run: jest.fn() })),
        toggleItalic: jest.fn(() => ({ run: jest.fn() })),
        toggleStrike: jest.fn(() => ({ run: jest.fn() })),
        toggleCode: jest.fn(() => ({ run: jest.fn() })),
        toggleHeading: jest.fn(() => ({ run: jest.fn() })),
        toggleBulletList: jest.fn(() => ({ run: jest.fn() })),
        toggleOrderedList: jest.fn(() => ({ run: jest.fn() })),
        toggleCodeBlock: jest.fn(() => ({ run: jest.fn() })),
        setLink: jest.fn(() => ({ run: jest.fn() })),
        unsetLink: jest.fn(() => ({ run: jest.fn() })),
        sinkListItem: jest.fn(() => ({ run: jest.fn() })),
        liftListItem: jest.fn(() => ({ run: jest.fn() })),
      })),
    })),
    isActive: jest.fn(() => false),
    isFocused: false,
    state: {
      selection: {
        from: 0,
        to: 0,
        empty: true,
      },
      doc: {
        textBetween: jest.fn(() => ''),
      },
    },
  })),
  EditorContent: ({ editor }: any) => <div data-testid="editor-content">Editor Content</div>,
}));

describe('EnhancedRichTextEditor', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock window.innerWidth for mobile tests
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
  });

  it('renders with basic props', () => {
    render(
      <EnhancedRichTextEditor
        value=""
        onChange={mockOnChange}
        placeholder="Start typing..."
      />
    );

    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('shows toolbar when focused', async () => {
    render(
      <EnhancedRichTextEditor
        value=""
        onChange={mockOnChange}
        placeholder="Start typing..."
      />
    );

    const editor = screen.getByTestId('editor-content');
    fireEvent.focus(editor);

    // Wait for toolbar to appear
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /bold/i })).toBeInTheDocument();
    });
  });

  it('displays character count when maxLength is provided', () => {
    render(
      <EnhancedRichTextEditor
        value="test content"
        onChange={mockOnChange}
        maxLength={100}
      />
    );

    expect(screen.getByText('12/100')).toBeInTheDocument();
  });

  it('shows warning when approaching character limit', () => {
    render(
      <EnhancedRichTextEditor
        value={"a".repeat(95)}
        onChange={mockOnChange}
        maxLength={100}
      />
    );

    const characterCount = screen.getByText('95/100');
    expect(characterCount).toHaveClass('text-warning');
  });

  it('shows error when exceeding character limit', () => {
    render(
      <EnhancedRichTextEditor
        value={"a".repeat(105)}
        onChange={mockOnChange}
        maxLength={100}
      />
    );

    const characterCount = screen.getByText('105/100');
    expect(characterCount).toHaveClass('text-destructive');
  });

  it('includes accessibility attributes', () => {
    render(
      <EnhancedRichTextEditor
        value=""
        onChange={mockOnChange}
        placeholder="Start typing..."
        id="test-editor"
      />
    );

    // Check for screen reader description
    expect(screen.getByText(/Rich text editor with markdown support/)).toBeInTheDocument();
  });

  it('handles disabled state', () => {
    render(
      <EnhancedRichTextEditor
        value=""
        onChange={mockOnChange}
        disabled={true}
      />
    );

    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('shows toolbar on mobile when editor has content', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500,
    });

    render(
      <EnhancedRichTextEditor
        value="some content"
        onChange={mockOnChange}
      />
    );

    expect(screen.getByRole('button', { name: /bold/i })).toBeInTheDocument();
  });

  it('handles markdown toggle and shows textarea while keeping toolbar for toggling/formatting', async () => {
    render(
      <EnhancedRichTextEditor
        value=""
        onChange={mockOnChange}
        placeholder="Start typing..."
      />
    );

    const editor = screen.getByTestId('editor-content');
    fireEvent.focus(editor);

    const markdownToggle = await screen.findByRole('button', { name: /markdown syntax/i });
    fireEvent.click(markdownToggle);

    // After toggling, the markdown textarea should be visible and the toolbar remains for toggling only
    expect(await screen.findByTestId('markdown-textarea')).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /bold/i })).not.toBeInTheDocument();

    // Toggle back to WYSIWYG
    fireEvent.click(markdownToggle);
    expect(screen.queryByTestId('markdown-textarea')).not.toBeInTheDocument();
  });

  describe('Toolbar functionality', () => {
    beforeEach(async () => {
      render(
        <EnhancedRichTextEditor
          value=""
          onChange={mockOnChange}
        />
      );

      const editor = screen.getByTestId('editor-content');
      fireEvent.focus(editor);

      // Wait for toolbar to appear
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /bold/i })).toBeInTheDocument();
      });
    });

    it('has bold button', () => {
      expect(screen.getByRole('button', { name: /bold/i })).toBeInTheDocument();
    });

    it('has italic button', () => {
      expect(screen.getByRole('button', { name: /italic/i })).toBeInTheDocument();
    });

    it('has strikethrough button', () => {
      expect(screen.getByRole('button', { name: /strikethrough/i })).toBeInTheDocument();
    });

    it('has code button', () => {
      expect(screen.getByRole('button', { name: /inline code/i })).toBeInTheDocument();
    });

    it('does not render heading buttons (H1-H3 removed)', () => {
      expect(screen.queryByRole('button', { name: /heading 1/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /heading 2/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /heading 3/i })).not.toBeInTheDocument();
    });

    it('has list buttons', () => {
      expect(screen.getByRole('button', { name: /bullet list/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /numbered list/i })).toBeInTheDocument();
    });

    it('has advanced formatting buttons', () => {
      expect(screen.getByRole('button', { name: /link/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /code block/i })).toBeInTheDocument();
    });
  });

  describe('Keyboard shortcuts', () => {
    it('shows keyboard shortcuts hint on desktop', async () => {
      render(
        <EnhancedRichTextEditor
          value=""
          onChange={mockOnChange}
        />
      );

      const editor = screen.getByTestId('editor-content');
      fireEvent.focus(editor);

      await waitFor(() => {
        expect(screen.getByText(/Ctrl\+B bold/)).toBeInTheDocument();
      });
    });
  });

  describe('Mobile optimizations', () => {
    beforeEach(() => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 500,
      });
    });

    it('applies touch-manipulation styles', () => {
      render(
        <EnhancedRichTextEditor
          value=""
          onChange={mockOnChange}
        />
      );

      const editorContainer = screen.getByTestId('editor-content').parentElement;
      expect(editorContainer).toHaveClass('touch-manipulation');
    });

    it('shows toolbar when content exists on mobile', () => {
      render(
        <EnhancedRichTextEditor
          value="some content"
          onChange={mockOnChange}
        />
      );

      expect(screen.getByRole('button', { name: /bold/i })).toBeInTheDocument();
    });
  });

  describe('Integration with markdown utilities', () => {
    it('calls markdownToHtml when converting content', () => {
      const { markdownToHtml } = require('@/lib/markdown-utils');

      render(
        <EnhancedRichTextEditor
          value="# Test Header"
          onChange={mockOnChange}
        />
      );

      expect(markdownToHtml).toHaveBeenCalledWith('# Test Header');
    });

    it('handles empty content gracefully', () => {
      render(
        <EnhancedRichTextEditor
          value=""
          onChange={mockOnChange}
        />
      );

      expect(screen.getByTestId('editor-content')).toBeInTheDocument();
    });
  });
});
