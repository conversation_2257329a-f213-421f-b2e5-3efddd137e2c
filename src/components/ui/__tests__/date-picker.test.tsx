import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { DatePicker } from '../date-picker';
import { format } from 'date-fns';

// Mock the format function from date-fns
jest.mock('date-fns', () => {
  const actual = jest.requireActual('date-fns');
  return {
    ...actual,
    format: jest.fn((date, formatStr) => {
      if (formatStr === 'MMM d, yyyy') {
        return 'Jan 1, 2023';
      }
      if (formatStr === 'yyyy-MM-dd') {
        return '2023-01-01';
      }
      return actual.format(date, formatStr);
    }),
  };
});

describe('DatePicker', () => {
  const mockSetDate = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with placeholder when no date is provided', () => {
    render(<DatePicker date={undefined} setDate={mockSetDate} placeholder="Select date" />);
    
    expect(screen.getByText('Select date')).toBeInTheDocument();
  });

  it('renders with formatted date when date is provided', () => {
    const date = new Date(2023, 0, 1); // January 1, 2023
    render(<DatePicker date={date} setDate={mockSetDate} />);
    
    expect(screen.getByText('January 1, 2023')).toBeInTheDocument();
  });

  it('opens calendar when button is clicked', () => {
    render(<DatePicker date={undefined} setDate={mockSetDate} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    // Check if calendar is visible
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  it('calls setDate when a date is selected', () => {
    const date = new Date(2023, 0, 1);
    render(<DatePicker date={date} setDate={mockSetDate} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    // Find and click a day button (this is simplified as we'd need to mock the DayPicker component more thoroughly)
    const dayButtons = screen.getAllByRole('button');
    // Assuming the third button is a day button
    if (dayButtons.length > 2) {
      fireEvent.click(dayButtons[2]);
      expect(mockSetDate).toHaveBeenCalled();
    }
  });

  it('renders with label when provided', () => {
    render(<DatePicker date={undefined} setDate={mockSetDate} label="Due Date" />);
    
    expect(screen.getByText('Due Date')).toBeInTheDocument();
  });

  it('shows input field when showInputField is true', () => {
    render(<DatePicker date={undefined} setDate={mockSetDate} showInputField />);
    
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('updates date when input field changes with valid date', () => {
    render(<DatePicker date={undefined} setDate={mockSetDate} showInputField />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '2023-01-01' } });
    
    expect(mockSetDate).toHaveBeenCalled();
  });

  it('shows error message when input field has invalid date', () => {
    render(<DatePicker date={undefined} setDate={mockSetDate} showInputField />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'invalid-date' } });
    
    expect(screen.getByText('Please enter a valid date in YYYY-MM-DD format')).toBeInTheDocument();
  });

  it('disables the date picker when disabled prop is true', () => {
    render(<DatePicker date={undefined} setDate={mockSetDate} disabled />);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });

  it('clears the date when clear button is clicked', () => {
    const date = new Date(2023, 0, 1);
    render(<DatePicker date={date} setDate={mockSetDate} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    const clearButton = screen.getByText('Clear');
    fireEvent.click(clearButton);
    
    expect(mockSetDate).toHaveBeenCalledWith(undefined);
  });
});
