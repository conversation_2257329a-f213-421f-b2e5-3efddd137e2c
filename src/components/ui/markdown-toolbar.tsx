"use client";

import * as React from "react";
import type { Editor } from "@tiptap/react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Code,
  Code2,
  FileCode,
  Link,
  Strikethrough,
  Type as TypeIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export interface MarkdownToolbarProps {
  onFormat: (format: string, prefix?: string, suffix?: string) => void;
  showMarkdown: boolean;
  onToggleMarkdown: () => void;
  className?: string;
  editor?: Editor | null; // Tiptap editor instance
}

export function MarkdownToolbar({ onFormat, showMarkdown, onToggleMarkdown, className, editor }: MarkdownToolbarProps) {
  const toolbarButtons = [
    {
      icon: Bold,
      label: "Bold (Ctrl+B)",
      format: "bold",
      prefix: "**",
      suffix: "**",
      shortcut: "Ctrl+B",
    },
    {
      icon: Italic,
      label: "Italic (Ctrl+I)",
      format: "italic",
      prefix: "*",
      suffix: "*",
      shortcut: "Ctrl+I",
    },
    {
      icon: Strikethrough,
      label: "Strikethrough",
      format: "strikethrough",
      prefix: "~~",
      suffix: "~~",
    },
    {
      icon: Code2,
      label: "Inline Code",
      format: "code",
      prefix: "`",
      suffix: "`",
    },
  ];


  const listButtons = [
    {
      icon: List,
      label: "Bullet List",
      format: "bulletList",
      prefix: "- ",
    },
    {
      icon: ListOrdered,
      label: "Numbered List",
      format: "orderedList",
      prefix: "1. ",
    },
  ];

  const advancedButtons = [
    {
      icon: Link,
      label: "Link",
      format: "link",
      prefix: "[",
      suffix: "](url)",
    },
    {
      icon: Code,
      label: "Code Block",
      format: "codeBlock",
      prefix: "```\n",
      suffix: "\n```",
    },
  ];

  const renderButtonGroup = (buttons: typeof toolbarButtons, groupKey: string) => (
    <React.Fragment key={groupKey}>
      {buttons.map((button) => {
        const Icon = button.icon;
        const isActive = editor && (
          (button.format === 'bold' && editor.isActive('bold')) ||
          (button.format === 'italic' && editor.isActive('italic')) ||
          (button.format === 'strikethrough' && editor.isActive('strike')) ||
          (button.format === 'code' && editor.isActive('code')) ||
          (button.format === 'bulletList' && editor.isActive('bulletList')) ||
          (button.format === 'orderedList' && editor.isActive('orderedList')) ||
          (button.format === 'link' && editor.isActive('link')) ||
          (button.format === 'heading1' && editor.isActive('heading', { level: 1 })) ||
          (button.format === 'heading2' && editor.isActive('heading', { level: 2 })) ||
          (button.format === 'heading3' && editor.isActive('heading', { level: 3 })) ||
          (button.format === 'codeBlock' && editor.isActive('codeBlock'))
        );

        return (
          <Button
            key={button.format}
            variant="ghost"
            size="sm"
            className={cn(
              "h-7 w-7 p-0 hover:bg-muted",
              isActive && "bg-muted text-foreground"
            )}
            onClick={() => onFormat(button.format, button.prefix, button.suffix)}
            onMouseDown={(e) => e.preventDefault()}
            type="button"
            aria-label={button.label}
            title={button.label}
            data-toolbar="true"
          >
            <Icon className="h-3.5 w-3.5" />
          </Button>
        );
      })}
    </React.Fragment>
  );

  const headingLabel = React.useMemo(() => {
    if (!editor) return "Text";
    if (editor.isActive('heading', { level: 1 })) return 'H1';
    if (editor.isActive('heading', { level: 2 })) return 'H2';
    if (editor.isActive('heading', { level: 3 })) return 'H3';
    return 'Text';
  }, [editor?.state]);

  return (
    <div
      data-toolbar="true"
      className={cn(
        "flex items-center gap-1 p-2 border border-input bg-background rounded-md shadow-sm flex-wrap w-full",
        className
      )}
    >
      {/* Only show formatting controls in WYSIWYG mode */}
      {!showMarkdown && (
        <>
          {/* Text style dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0 hover:bg-muted"
                type="button"
                data-toolbar="true"
                onMouseDown={(e) => { e.preventDefault(); editor?.chain().focus().run(); }}
                aria-label={`${headingLabel} style`}
                title={`${headingLabel} style`}
              >
                <TypeIcon className="h-3.5 w-3.5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem
                onMouseDown={(e) => e.preventDefault()}
                onClick={() => { onFormat('paragraph'); editor?.chain().focus().run(); }}
                data-toolbar="true"
              >
                <span className="block text-base font-normal leading-relaxed">Regular Text</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onMouseDown={(e) => e.preventDefault()}
                onClick={() => { onFormat('heading1'); editor?.chain().focus().run(); }}
                data-toolbar="true"
              >
                <span className="block text-2xl font-semibold leading-tight">Header 1</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onMouseDown={(e) => e.preventDefault()}
                onClick={() => { onFormat('heading2'); editor?.chain().focus().run(); }}
                data-toolbar="true"
              >
                <span className="block text-xl font-semibold leading-tight">Header 2</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onMouseDown={(e) => e.preventDefault()}
                onClick={() => { onFormat('heading3'); editor?.chain().focus().run(); }}
                data-toolbar="true"
              >
                <span className="block text-lg font-semibold leading-snug">Header 3</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Separator */}
          <div className="w-px h-4 bg-border mx-1" />

          {/* Text formatting */}
          {renderButtonGroup(toolbarButtons, 'text')}

          {/* Separator */}
          <div className="w-px h-4 bg-border mx-1" />

          {/* Lists */}
          {renderButtonGroup(listButtons, 'lists')}

          {/* Separator */}
          <div className="w-px h-4 bg-border mx-1" />

          {/* Advanced */}
          {renderButtonGroup(advancedButtons, 'advanced')}

          {/* Separator */}
          <div className="w-px h-4 bg-border mx-1" />
        </>
      )}

      {/* Markdown toggle button (always visible) */}
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "ml-auto h-7 w-7 p-0 hover:bg-muted",
          showMarkdown && "bg-muted"
        )}
        onClick={onToggleMarkdown}
        onMouseDown={(e) => e.preventDefault()}
        type="button"
        aria-label={showMarkdown ? "Hide markdown syntax" : "Show markdown syntax"}
        title={showMarkdown ? "Hide markdown syntax" : "Show markdown syntax"}
        data-toolbar="true"
      >
        <FileCode className="h-3.5 w-3.5" />
      </Button>
    </div>
  );
}
