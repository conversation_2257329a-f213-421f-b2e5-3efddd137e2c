"use client";

import * as React from "react";
import { use<PERSON><PERSON><PERSON>, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Link from '@tiptap/extension-link';
import { Extension } from '@tiptap/core';
import { <PERSON>lug<PERSON>, PluginKey } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';
import { MobileDialog, MobileDialogContent, MobileDialogFooter, MobileDialogHeader, MobileDialogTitle } from "@/components/ui/mobile-dialog";
import { ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { MarkdownToolbar } from "@/components/ui/markdown-toolbar";
import { markdownToHtml, htmlToMarkdown } from "@/lib/markdown-utils";
import { cn } from "@/lib/utils";

export interface EnhancedRichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  id?: string;
  disabled?: boolean;
  maxLength?: number;
  autoFocus?: boolean;
  /**
   * Fixed height for the editor area. If provided, both modes (WYSIWYG/Markdown)
   * will render at exactly this height, and content scrolls internally.
   * Example: "240px".
   */
  height?: string;
  /**
   * Back-compat: if height is not provided, these are used.
   */
  minHeight?: string;
  maxHeight?: string;
}

export const EnhancedRichTextEditor = React.forwardRef<HTMLDivElement, EnhancedRichTextEditorProps>(
  ({
    value,
    onChange,
    placeholder,
    className,
    id,
    disabled = false,
    maxLength,
    autoFocus = false,
    height,
    minHeight = "120px",
    maxHeight = "400px"
  }, ref) => {
    const [showMarkdown, setShowMarkdown] = React.useState(false);
    const [isFocused, setIsFocused] = React.useState(false);
    const [expandedHeightPx, setExpandedHeightPx] = React.useState<number | null>(null);

    // Link dialog state
    const [linkDialogOpen, setLinkDialogOpen] = React.useState(false);
    const [linkUrl, setLinkUrl] = React.useState("");
    const [linkText, setLinkText] = React.useState("");
    const [linkError, setLinkError] = React.useState("");
    const [linkIsEdit, setLinkIsEdit] = React.useState(false);
    const linkContextRef = React.useRef<'markdown' | 'wysiwyg'>("wysiwyg");
    const mdSelectionRef = React.useRef<{ start: number; end: number }>({ start: 0, end: 0 });
    const wysiwygSelectionRef = React.useRef<{ from: number; to: number } | null>(null);
    const markdownTextareaRef = React.useRef<HTMLTextAreaElement | null>(null);

    // Code block toolbar extension: language dropdown + copy icon
    const CodeBlockToolbar = React.useMemo(() => {
      const codeToolbarKey = new PluginKey('codeBlockToolbar');
      const popularLangs = [
        { label: 'Plain text', value: '' },
        { label: 'JavaScript', value: 'javascript' },
        { label: 'TypeScript', value: 'typescript' },
        { label: 'Python', value: 'python' },
        { label: 'Bash', value: 'bash' },
        { label: 'JSON', value: 'json' },
        { label: 'HTML', value: 'html' },
        { label: 'CSS', value: 'css' },
        { label: 'SQL', value: 'sql' },
        { label: 'Markdown', value: 'markdown' },
      ];

      return Extension.create({
        name: 'codeBlockToolbar',
        addProseMirrorPlugins() {
          return [
            new Plugin({
              key: codeToolbarKey,
              state: {
                init: () => ({ langs: {} as Record<number, string> }),
                apply(tr, prev) {
                  const meta = tr.getMeta(codeToolbarKey) as { type: string; pos: number; lang?: string } | undefined;
                  if (meta?.type === 'setLang' && typeof meta.pos === 'number') {
                    return { langs: { ...prev.langs, [meta.pos]: meta.lang ?? '' } };
                  }
                  return prev;
                },
              },
              props: {
                decorations(state) {
                  const decos: any[] = [];
                  const { doc } = state;
                  const pluginState = codeToolbarKey.getState(state) as { langs: Record<number, string> };
                  doc.descendants((node, pos) => {
                    if (node.type && node.type.name === 'codeBlock') {
                      const widget = Decoration.widget(pos + 1, () => {
                        const wrapper = document.createElement('div');
                        wrapper.className = 'pm-code-toolbar';
                        wrapper.dataset.pos = String(pos);

                        // Language select
                        const sel = document.createElement('select');
                        sel.className = 'pm-code-lang-select';
                        for (const { label, value } of popularLangs) {
                          const opt = document.createElement('option');
                          opt.value = value; opt.textContent = label;
                          sel.appendChild(opt);
                        }
                        const current = pluginState?.langs?.[pos] ?? '';
                        sel.value = current;

                        // Copy icon-only button
                        const btn = document.createElement('button');
                        btn.type = 'button';
                        btn.className = 'pm-code-copy';
                        btn.setAttribute('aria-label', 'Copy code');
                        btn.textContent = '⧉';

                        wrapper.appendChild(sel);
                        wrapper.appendChild(btn);

                        return wrapper;
                      }, { side: -1 });
                      decos.push(widget);
                    }
                  });
                  return DecorationSet.create(doc, decos);
                },
                handleDOMEvents: {
                  click: (_view, event) => {
                    const target = event.target as HTMLElement | null;
                    if (!target) return false;
                    const copyBtn = target.closest('.pm-code-copy') as HTMLButtonElement | null;
                    if (copyBtn) {
                      event.preventDefault();
                      event.stopPropagation();
                      const pre = copyBtn.closest('pre');
                      const text = pre?.querySelector('code')?.textContent || pre?.textContent || '';
                      if (text) {
                        navigator.clipboard?.writeText(text).then(() => {
                          copyBtn.classList.add('copied');
                          setTimeout(() => { copyBtn.classList.remove('copied'); }, 1200);
                        });
                      }
                      return true;
                    }
                    return false;
                  },
                  change: (view, event) => {
                    const target = event.target as HTMLSelectElement | null;
                    if (!target || !target.classList.contains('pm-code-lang-select')) return false;
                    const toolbar = target.closest('.pm-code-toolbar') as HTMLDivElement | null;
                    const pos = toolbar?.dataset.pos ? Number(toolbar.dataset.pos) : NaN;
                    if (!Number.isFinite(pos)) return false;
                    const lang = target.value || '';
                    const tr = view.state.tr.setMeta(codeToolbarKey, { type: 'setLang', pos, lang });
                    view.dispatch(tr);
                    return true;
                  },
                },
              },
            }),
          ];
        },
      });
    }, []);

    // Tiptap editor setup with StarterKit (includes most features we need)
    const editor = useEditor({
      extensions: [
        StarterKit,
        CodeBlockToolbar,
        Placeholder.configure({
          placeholder: placeholder || 'Start typing...',
        }),
        Link.configure({
          autolink: true,
          linkOnPaste: true,
          openOnClick: false,
          HTMLAttributes: {
            rel: 'noopener noreferrer nofollow',
            target: '_blank',
          },
        }),
      ],

      content: markdownToHtml(value),
      editable: !disabled,
      autofocus: autoFocus,

      onUpdate: ({ editor }) => {
        // Only handle updates from the WYSIWYG editor
        const html = editor.getHTML();
        const markdown = htmlToMarkdown(html);
        const next = typeof maxLength === 'number' && maxLength > 0
          ? markdown.slice(0, maxLength)
          : markdown;
        if (next !== value) onChange(next);
      },

      onFocus: () => {
        setIsFocused(true);
        if (!height) {
          requestAnimationFrame(() => {
            const el = editorContainerRef.current;
            if (!el) return;
            const maxPx = parseInt((height ?? maxHeight).toString(), 10);
            const sh = el.scrollHeight;
            const clamped = Number.isFinite(maxPx) ? Math.min(sh, maxPx) : sh;
            setExpandedHeightPx(clamped);
          });
        }
      },
      onBlur: () => { setIsFocused(false); },

      editorProps: {
        attributes: {
          class: cn(
            'prose max-w-none focus:outline-none',
            `min-h-full px-3 py-2 text-sm`,
            'prose-headings:font-semibold prose-headings:text-foreground prose-headings:mt-2 prose-headings:mb-1',
            'prose-h1:text-2xl md:prose-h1:text-3xl prose-h1:leading-tight',
            'prose-h2:text-xl md:prose-h2:text-2xl prose-h2:leading-tight',
            'prose-h3:text-lg md:prose-h3:text-xl prose-h3:leading-snug',
            'h-full max-h-full',
            'prose-p:text-foreground prose-p:my-1 prose-p:leading-relaxed',
            'prose-li:text-foreground prose-li:my-0',
            'prose-strong:text-foreground prose-strong:font-semibold',
            'prose-em:text-foreground prose-em:italic',
            'prose-code:text-foreground prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-xs',
            'prose-pre:bg-muted prose-pre:text-foreground',
            'prose-mark:bg-amber-300 dark:prose-mark:bg-amber-400 prose-mark:text-black prose-mark:px-0.5 prose-mark:rounded-sm',
            'prose-a:text-primary prose-a:underline prose-a:decoration-2 prose-a:underline-offset-4 prose-a:cursor-pointer hover:opacity-90',
            'prose-ul:my-2 prose-ol:my-2',
            'prose-ul:list-disc prose-ol:list-decimal prose-ul:pl-5 prose-ol:pl-5',
            'prose-li:marker:text-foreground',
            // Mobile optimizations
            'touch-manipulation', // Optimize touch interactions
            'selection:bg-primary/90', // Better selection visibility
          ),
          role: 'textbox',
          'aria-label': placeholder || 'Rich text editor',
          'aria-multiline': 'true',
          'aria-describedby': id ? `${id}-description` : '',
          spellcheck: 'true',
          autocorrect: 'on',
          autocapitalize: 'sentences',
        },

        handleKeyDown: (_view, event) => {
          // Handle keyboard shortcuts
          if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
              case 'b':
                event.preventDefault();
                editor?.chain().focus().toggleBold().run();
                return true;
              case 'i':
                event.preventDefault();
                editor?.chain().focus().toggleItalic().run();
                return true;
              case 'k':
                event.preventDefault();
                handleLinkToggle();
                return true;
              case 'e':
                event.preventDefault();
                editor?.chain().focus().toggleCode().run();
                return true;
            }
          }

          // Handle Tab always
          if (event.key === 'Tab') {
            event.preventDefault();
            if (editor?.isActive('listItem')) {
              if (event.shiftKey) {
                editor.chain().focus().liftListItem('listItem').run();
              } else {
                editor.chain().focus().sinkListItem('listItem').run();
              }
              return true;
            }
            // Outside lists: insert or remove spaces
            if (event.shiftKey) {
              const from = editor?.state.selection.from ?? 0;
              const start = Math.max(0, from - 2);
              const before = editor?.state.doc.textBetween(start, from, '\n', '\n') ?? '';
              if (before.endsWith('  ')) {
                editor?.chain().focus().deleteRange({ from: from - 2, to: from }).run();
              } else if (before.endsWith(' ')) {
                editor?.chain().focus().deleteRange({ from: from - 1, to: from }).run();
              }
            } else {
              editor?.chain().focus().insertContent('  ').run();
            }
            return true;
          }

          return false;
        },
      },
    });

    const editorContainerRef = React.useRef<HTMLDivElement | null>(null);
    const [lockedHeight, setLockedHeight] = React.useState<string | null>(null);

    const handleEditorMouseDown = React.useCallback((e: React.MouseEvent) => {
      const target = e.target as HTMLElement | null;
      if (!target) return;
      const anchor = target.closest('a');
      if (!anchor) return;
      if (!editor) return;

      const href = anchor.getAttribute('href');
      if (!href || href === '#' || href.startsWith('javascript:')) {
        e.preventDefault();
        return;
      }

      const isModifier = (e.metaKey || e.ctrlKey);
      const shouldNavigate = !editor.isFocused || isModifier;
      if (!shouldNavigate) {
        // Editing without modifier: do not navigate
        e.preventDefault();
        return;
      }

      // Navigate explicitly and avoid focusing the editor
      e.preventDefault();
      window.open(href, '_blank', 'noopener,noreferrer');
    }, [editor]);

    const handleContainerMouseDown = React.useCallback((e: React.MouseEvent) => {
      if (showMarkdown) return;
      const target = e.target as HTMLElement | null;
      if (!target) return;
      if (target.closest('a')) return; // links handled separately
      if (editor && !editor.isFocused) {
        editor.chain().focus().run();
      }
    }, [editor, showMarkdown]);

    // Update editor content when value or mode changes
    React.useEffect(() => {
      if (editor && !showMarkdown && !editor.isFocused) {
        const newContent = markdownToHtml(value);
        if (editor.getHTML() !== newContent) {
          editor.commands.setContent(newContent);
        }
      }
    }, [value, editor, showMarkdown]);

    // Handle format commands from toolbar in both modes
    const handleFormat = (format: string) => {
      if (showMarkdown) {
        const ta = markdownTextareaRef.current;
        if (!ta) return;
        const start = ta.selectionStart ?? 0;
        const end = ta.selectionEnd ?? 0;
        const selectedText = value.slice(start, end);

        const applyInline = (prefix: string, suffix: string = prefix) => {
          if (selectedText) {
            // Toggle if already wrapped
            const before = value.slice(Math.max(0, start - prefix.length), start);
            const after = value.slice(end, end + suffix.length);
            let newText: string;
            let cursorPos = end;
            if (before === prefix && after === suffix) {
              newText = value.slice(0, start - prefix.length) + selectedText + value.slice(end + suffix.length);
              cursorPos = start - prefix.length + selectedText.length;
            } else {
              newText = value.slice(0, start) + prefix + selectedText + suffix + value.slice(end);
              cursorPos = start + prefix.length + selectedText.length + suffix.length;
            }
            onChange(newText);
            setTimeout(() => { ta.setSelectionRange(cursorPos, cursorPos); ta.focus(); }, 0);
          } else {
            const insertion = prefix + suffix;
            const cursorPos = start + prefix.length;
            const newText = value.slice(0, start) + insertion + value.slice(end);
            onChange(newText);
            setTimeout(() => { ta.setSelectionRange(cursorPos, cursorPos); ta.focus(); }, 0);
          }
        };

        const applyListPrefix = (linePrefix: string) => {
          const lines = value.split('\n');
          const startLine = value.slice(0, start).split('\n').length - 1;
          const endLine = value.slice(0, end).split('\n').length - 1;
          for (let i = startLine; i <= endLine; i++) {
            if (!lines[i]) continue;
            const trimmed = lines[i].trimStart();
            if (!trimmed.startsWith(linePrefix.trim())) {
              lines[i] = linePrefix + lines[i];
            }
          }
          const newText = lines.join('\n');
          const delta = newText.length - value.length;
          onChange(newText);
          setTimeout(() => { const pos = end + delta; ta.setSelectionRange(pos, pos); ta.focus(); }, 0);
        };

        switch (format) {
          case 'bold':
            applyInline('**');
            break;
          case 'italic':
            applyInline('*');
            break;
          case 'strikethrough':
            applyInline('~~');
            break;
          case 'code':
            applyInline('`');
            break;
          case 'bulletList':
            applyListPrefix('- ');
            break;
          case 'orderedList':
            applyListPrefix('1. ');
            break;
          case 'link': {
            const url = window.prompt('Enter URL:');
            if (url) {
              const text = selectedText || window.prompt('Enter link text:') || 'link';
              const newText = value.slice(0, start) + `[${text}](${url})` + value.slice(end);
              const cursorPos = start + `[${text}](${url})`.length;
              onChange(newText);
              setTimeout(() => { ta.setSelectionRange(cursorPos, cursorPos); ta.focus(); }, 0);
            }
            break;
          }
          case 'codeBlock': {
            const block = selectedText ? `\n\`\`\`\n${selectedText}\n\`\`\`\n` : `\n\`\`\`\ncode\n\`\`\`\n`;
            const newText = value.slice(0, start) + block + value.slice(end);
            const cursorPos = start + block.length;
            onChange(newText);
            setTimeout(() => { ta.setSelectionRange(cursorPos, cursorPos); ta.focus(); }, 0);
            break;
          }
        }
        return;
      }

      // WYSIWYG mode
      if (!editor) return;
      switch (format) {
        case 'bold': editor.chain().focus().toggleBold().run(); break;
        case 'italic': editor.chain().focus().toggleItalic().run(); break;
        case 'strikethrough': editor.chain().focus().toggleStrike().run(); break;
        case 'code': editor.chain().focus().toggleCode().run(); break;
        case 'bulletList': editor.chain().focus().toggleBulletList().run(); break;
        case 'orderedList': editor.chain().focus().toggleOrderedList().run(); break;
        case 'link': handleLinkToggle(); break;
        case 'codeBlock': editor.chain().focus().toggleCodeBlock().run(); break;
        case 'paragraph': editor.chain().focus().setParagraph().run(); break;
        case 'heading1': if (!editor.isActive('heading', { level: 1 })) editor.chain().focus().toggleHeading({ level: 1 }).run(); break;
        case 'heading2': if (!editor.isActive('heading', { level: 2 })) editor.chain().focus().toggleHeading({ level: 2 }).run(); break;
        case 'heading3': if (!editor.isActive('heading', { level: 3 })) editor.chain().focus().toggleHeading({ level: 3 }).run(); break;
      }
    };

    const handleLinkToggle = () => {
      setLinkError("");

      if (showMarkdown) {
        const ta = markdownTextareaRef.current;
        if (!ta) return;
        const start = ta.selectionStart ?? 0;
        const end = ta.selectionEnd ?? 0;
        mdSelectionRef.current = { start, end };
        const selectedText = value.slice(start, end);
        setLinkText(selectedText);
        setLinkUrl("");
        setLinkIsEdit(false);
        linkContextRef.current = 'markdown';
        setLinkDialogOpen(true);
        return;
      }

      if (!editor) return;
      const { from, to } = editor.state.selection;
      wysiwygSelectionRef.current = { from, to };
      linkContextRef.current = 'wysiwyg';

      if (editor.isActive('link')) {
        const currentHref = editor.getAttributes('link')?.href || "";
        const selected = editor.state.doc.textBetween(from, to) || "";
        setLinkUrl(currentHref);
        setLinkText(selected);
        setLinkIsEdit(true);
      } else {
        const selected = editor.state.doc.textBetween(from, to) || "";
        setLinkUrl("");
        setLinkText(selected);
        setLinkIsEdit(false);
      }
      setLinkDialogOpen(true);
    };

    const confirmLink = () => {
      let url = linkUrl.trim();
      if (!url) { setLinkError('URL is required'); return; }
      if (!/^https?:\/\//i.test(url) && !/^mailto:/i.test(url)) url = `https://${url}`;
      try { new URL(url); } catch { setLinkError('Invalid URL'); return; }
      const text = linkText.trim();

      if (linkContextRef.current === 'markdown') {
        const { start, end } = mdSelectionRef.current;
        const insertion = `[${text || url}](${url})`;
        const newText = value.slice(0, start) + insertion + value.slice(end);
        onChange(newText);
        setLinkDialogOpen(false);
        setTimeout(() => {
          const ta = markdownTextareaRef.current; if (ta) {
            const pos = start + insertion.length; ta.setSelectionRange(pos, pos); ta.focus();
          }
        }, 0);
        return;
      }

      if (!editor) return;
      const sel = wysiwygSelectionRef.current;
      let chain = editor.chain().focus();
      if (sel) chain = chain.setTextSelection({ from: sel.from, to: sel.to });

      if (linkIsEdit || editor.isActive('link')) {
        chain.extendMarkRange('link').setLink({ href: url, target: '_blank', rel: 'noopener noreferrer nofollow' }).run();
      } else {
        const isEmpty = editor.state.selection.empty;
        if (isEmpty) {
          const content = `<a href="${url}" target="_blank" rel="noopener noreferrer nofollow">${text || url}</a>`;
          chain.insertContent(content).run();
        } else {
          chain.setLink({ href: url, target: '_blank', rel: 'noopener noreferrer nofollow' }).run();
        }
      }
      setLinkDialogOpen(false);
    };

    const removeLink = () => {
      if (linkContextRef.current !== 'wysiwyg' || !editor) { setLinkDialogOpen(false); return; }
      const sel = wysiwygSelectionRef.current;
      let chain = editor.chain().focus();
      if (sel) chain = chain.setTextSelection({ from: sel.from, to: sel.to });
      chain.extendMarkRange('link').unsetLink().run();
      setLinkDialogOpen(false);
    };

    const handleToggleMarkdown = () => {
      // Lock current container height to prevent layout jump during mode toggle
      const h = editorContainerRef.current?.getBoundingClientRect().height;
      if (h && Number.isFinite(h)) {
        setLockedHeight(`${Math.round(h)}px`);
      }

      setShowMarkdown((prev) => {
        const next = !prev;
        setTimeout(() => {
          if (next) {
            markdownTextareaRef.current?.focus();
          } else {
            editor?.chain().focus();
          }
        }, 0);

        // Unlock after the next paint so the outside container keeps the same size during toggle
        requestAnimationFrame(() => {
          requestAnimationFrame(() => setLockedHeight(null));
        });

        return next;
      });
    };

    return (
      <div ref={ref} className={cn("space-y-2", className)}>
        {/* Screen reader description */}
        {id && (
          <div id={`${id}-description`} className="sr-only">
            Rich text editor with markdown support. Use keyboard shortcuts like Ctrl+B for bold, Ctrl+I for italic.
            Press Tab to indent lists, Shift+Tab to outdent. Type markdown syntax for auto-formatting.
          </div>
        )}

        {/* Toolbar - always visible (desktop matches mobile behavior) */}
        <MarkdownToolbar
          onFormat={handleFormat}
          showMarkdown={showMarkdown}
          onToggleMarkdown={handleToggleMarkdown}
          editor={editor}
          className={cn(
            "animate-in fade-in-0 slide-in-from-top-1 duration-200",
            // Mobile optimizations
            "md:flex-nowrap flex-wrap gap-1",
            "sticky top-0 z-10 bg-background/95 backdrop-blur-sm"
          )}
        />

        {/* Editor */}
        <div
          ref={editorContainerRef}
          className={cn(
            "rounded-md border border-input bg-background/70 dark:bg-input/30 text-foreground",
            "focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]",
            height ? `h-[${height}]` : `max-h-[${maxHeight}]`,
            "overflow-y-auto",
            showMarkdown && "font-mono text-xs",
            // Mobile optimizations
            "touch-manipulation",
            // Better focus indicators for accessibility
            "focus-within:ring-offset-2 focus-within:ring-offset-background"
          )}
          style={{
            minHeight: height ?? minHeight,
            maxHeight: height ?? maxHeight,
            height: height ?? (lockedHeight ?? (isFocused ? (expandedHeightPx ? `${expandedHeightPx}px` : undefined) : minHeight)),
          }}
          onMouseDown={handleContainerMouseDown}
          // Touch event handlers for mobile
          onTouchStart={(e) => {
            // Prevent zoom on double tap for better UX
            e.currentTarget.style.touchAction = 'manipulation';
          }}
        >
          {showMarkdown ? (
            <textarea
              ref={markdownTextareaRef}
              data-testid="markdown-textarea"
              value={value}
              onChange={(e) => {
                const next = typeof maxLength === 'number' && maxLength > 0
                  ? e.target.value.slice(0, maxLength)
                  : e.target.value;
                onChange(next);
              }}
              onFocus={() => {
                setIsFocused(true);
                if (!height) {
                  setTimeout(() => {
                    const el = editorContainerRef.current;
                    if (!el) return;
                    const maxPx = parseInt((height ?? maxHeight).toString(), 10);
                    const sh = el.scrollHeight;
                    const clamped = Number.isFinite(maxPx) ? Math.min(sh, maxPx) : sh;
                    setExpandedHeightPx(clamped);
                  });
                }
              }}
              onBlur={() => setIsFocused(false)}
              disabled={disabled}
              placeholder={placeholder || 'Start typing...' }
              aria-label={placeholder || 'Markdown editor'}
              className="w-full h-full min-h-full max-h-full bg-transparent outline-none resize-none px-3 py-2 text-sm font-mono text-foreground placeholder:text-muted-foreground caret-primary selection:bg-primary/90"
              style={{ minHeight: height ?? minHeight, maxHeight: height ?? maxHeight }}
              onKeyDown={(e) => {
                if (e.key === 'Tab') {
                  e.preventDefault();
                  const ta = e.currentTarget;
                  const start = ta.selectionStart ?? 0;
                  const end = ta.selectionEnd ?? 0;
                  const before = value.slice(0, start);
                  const selected = value.slice(start, end);
                  const after = value.slice(end);
                  const isMultiline = selected.includes('\n');
                  const indent = '  ';

                  if (e.shiftKey) {
                    // Outdent
                    if (isMultiline) {
                      const newSelected = selected
                        .split('\n')
                        .map(line => line.startsWith(indent) ? line.slice(indent.length) : line.replace(/^\s{1,2}/, ''))
                        .join('\n');
                      const newValue = before + newSelected + after;
                      onChange(newValue);
                      const removed = selected.length - newSelected.length;
                      setTimeout(() => ta.setSelectionRange(start, end - removed));
                    } else {
                      // Single line outdent
                      const lineStart = value.lastIndexOf('\n', start - 1) + 1;
                      const currentLine = value.slice(lineStart, start);
                      let removeCount = 0;
                      if (currentLine.startsWith(indent)) removeCount = indent.length;
                      else if (/^\s/.test(currentLine)) removeCount = 1;
                      const newBefore = value.slice(0, lineStart) + currentLine.slice(removeCount);
                      const newValue = newBefore + value.slice(start);
                      const newPos = start - removeCount;
                      onChange(newValue);
                      setTimeout(() => ta.setSelectionRange(newPos, newPos));
                    }
                  } else {
                    // Indent
                    if (isMultiline) {
                      const newSelected = selected
                        .split('\n')
                        .map(line => indent + line)
                        .join('\n');
                      const newValue = before + newSelected + after;
                      const added = newSelected.length - selected.length;
                      onChange(newValue);
                      setTimeout(() => ta.setSelectionRange(start + indent.length, end + added));
                    } else {
                      const newValue = before + indent + selected + after;
                      onChange(newValue);
                      const newPos = start + indent.length;
                      setTimeout(() => ta.setSelectionRange(newPos, newPos));
                    }
                  }
                }
              }}
            />
          ) : (
            <EditorContent editor={editor} onMouseDownCapture={handleEditorMouseDown} />
          )}
        </div>

        {/* Character count */}
        {maxLength && (
          <div className="flex justify-end items-center text-xs text-muted-foreground">
            <div className={cn(
              "tabular-nums",
              value.length > maxLength * 0.9 && "text-warning",
              value.length >= maxLength && "text-destructive"
            )}>
              {value.length}/{maxLength}
            </div>
          </div>
        )}

        {/* Link Dialog - bottom-anchored on mobile */}
        <MobileDialog open={linkDialogOpen} onOpenChange={setLinkDialogOpen}>
          <MobileDialogContent className="md:max-w-md md:rounded-md md:border md:shadow-sm md:p-6 px-4 pb-4 pt-2 max-h-[85dvh] md:max-h-[80vh]" fullHeight={false} enableSwipeToDismiss>
            <MobileDialogHeader>
              <MobileDialogTitle>{linkIsEdit ? 'Edit link' : 'Insert link'}</MobileDialogTitle>
            </MobileDialogHeader>
            <form
              onSubmit={(e) => { e.preventDefault(); confirmLink(); }}
              className="space-y-3"
            >
              <div className="space-y-1.5">
                <Label htmlFor="link-url" className="flex items-center gap-2">URL <ExternalLink className="h-3.5 w-3.5 text-muted-foreground" /></Label>
                <Input
                  id="link-url"
                  placeholder="https://example.com"
                  value={linkUrl}
                  inputMode="url"
                  autoFocus
                  onChange={(e) => { setLinkUrl(e.target.value); setLinkError(""); }}
                />
                {linkError && <p className="text-xs text-destructive">{linkError}</p>}
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="link-text">Link text (optional)</Label>
                <Input
                  id="link-text"
                  placeholder="Display text"
                  value={linkText}
                  onChange={(e) => setLinkText(e.target.value)}
                />
              </div>
              <MobileDialogFooter className="gap-2">
                {linkIsEdit && linkContextRef.current === 'wysiwyg' && (
                  <Button type="button" variant="outline" onClick={removeLink}>
                    Remove link
                  </Button>
                )}
                <div className="ml-auto flex gap-2">
                  <Button type="button" variant="ghost" onClick={() => setLinkDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    {linkIsEdit ? 'Save' : 'Insert'}
                  </Button>
                </div>
              </MobileDialogFooter>
            </form>
          </MobileDialogContent>
        </MobileDialog>

      </div>
    );
  }
);

EnhancedRichTextEditor.displayName = "EnhancedRichTextEditor";
