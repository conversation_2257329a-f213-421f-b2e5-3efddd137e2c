"use client";

import { useState, useRef, useEffect } from "react";
import { useUser } from "@stackframe/stack";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { EnhancedRichTextEditor } from "@/components/ui/enhanced-rich-text-editor";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ChevronLeft, Search } from "lucide-react";
import { Space } from "@/lib/db";
import { useAddSpaceMutation } from "@/lib/queries";
import { SPACE_ICONS, renderSpaceIcon } from "@/lib/space-icons";
import { useUsageBanner } from "@/components/ui/usage-banner";
import { getUsageLimitMessage } from "@/lib/usage-messages";



interface CreateSpaceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSpaceCreated: (newSpace: Space) => void;
}

export function CreateSpaceModal({
  open,
  onOpenChange,
  onSpaceCreated,
}: CreateSpaceModalProps) {
  const user = useUser();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [selectedIcon, setSelectedIcon] = useState<string>("clipboard");
  const [iconSearch, setIconSearch] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // Auto-save refs
  const nameTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const descriptionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // TanStack Query mutation
  const addSpaceMutation = useAddSpaceMutation(user?.id || "");

  // Usage banner hook
  const { showUsageBanner } = useUsageBanner();

  // Filter icons based on search
  const filteredIcons = iconSearch
    ? SPACE_ICONS.filter(icon =>
        icon.toLowerCase().includes(iconSearch.toLowerCase())
      )
    : SPACE_ICONS;

  const handleClose = () => {
    // Clear any pending timeouts
    if (nameTimeoutRef.current) {
      clearTimeout(nameTimeoutRef.current);
    }
    if (descriptionTimeoutRef.current) {
      clearTimeout(descriptionTimeoutRef.current);
    }
    
    // Reset form
    setName("");
    setDescription("");
    setSelectedIcon("📋");
    setIconSearch("");
    setError("");
    onOpenChange(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      setError("Space name is required");
      return;
    }

    if (!user) {
      setError("You must be logged in to create a space");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const result = await addSpaceMutation.mutateAsync({
        name: name.trim(),
        description: description.trim() || null,
        icon: selectedIcon,
      });

      if (result) {
        onSpaceCreated(result);
        handleClose();
      } else {
        setError("Failed to create space. Please try again.");
      }
    } catch (err) {
      console.error("Error creating space:", err);

      // Check if it's a usage limit error
      if (err instanceof Error && err.message === 'USAGE_LIMIT_EXCEEDED') {
        showUsageBanner(getUsageLimitMessage('space'));
        setError(""); // Clear form error since we're showing banner
      } else {
        setError("An error occurred. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-save functionality (for future enhancement)
  const handleNameChange = (value: string) => {
    setName(value);
    setError("");
    
    // Clear existing timeout
    if (nameTimeoutRef.current) {
      clearTimeout(nameTimeoutRef.current);
    }
    
    // Set new timeout for auto-save (could be implemented later)
    nameTimeoutRef.current = setTimeout(() => {
      // Auto-save logic could go here
    }, 1000);
  };

  const handleDescriptionChange = (value: string) => {
    setDescription(value);
    
    // Clear existing timeout
    if (descriptionTimeoutRef.current) {
      clearTimeout(descriptionTimeoutRef.current);
    }
    
    // Set new timeout for auto-save (could be implemented later)
    descriptionTimeoutRef.current = setTimeout(() => {
      // Auto-save logic could go here
    }, 1000);
  };

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (nameTimeoutRef.current) {
        clearTimeout(nameTimeoutRef.current);
      }
      if (descriptionTimeoutRef.current) {
        clearTimeout(descriptionTimeoutRef.current);
      }
    };
  }, []);

  return (
    <MobileDialog open={open} onOpenChange={onOpenChange}>
      <MobileDialogContent className="sm:max-w-[640px]" fullHeight>
        <VisuallyHidden asChild>
          <MobileDialogTitle>New Space</MobileDialogTitle>
        </VisuallyHidden>
        <MobileDialogHeader className="flex items-start justify-start px-4 pt-4 pb-0 md:px-6 md:pt-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back</span>
          </Button>
        </MobileDialogHeader>

        <form onSubmit={handleSubmit} className="flex-1 flex flex-col">
          <div className="flex-1 overflow-y-auto px-4 pb-4 md:px-6">
            <div className="space-y-4">
              {/* Space Name */}
              <div className="space-y-2">
                <Input
                  id="name"
                  type="search"
                  value={name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="Space name"
                  autoComplete="off"
                  spellCheck="false"
                  disabled={isSubmitting}
                  maxLength={100}
                />
              </div>

              {/* Space Description */}
              <div className="space-y-2">
                <EnhancedRichTextEditor
                  id="description"
                  value={description}
                  onChange={handleDescriptionChange}
                  placeholder="Add a description for this space (optional)"
                  disabled={isSubmitting}
                  maxLength={5000}
                  height="240px"
                />
              </div>

              {/* Icon Selection */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Icon</span>
                  <div className="text-2xl">{renderSpaceIcon(selectedIcon, "h-6 w-6")}</div>
                </div>
                
                {/* Icon Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search icons..."
                    value={iconSearch}
                    onChange={(e) => setIconSearch(e.target.value)}
                    className="pl-10"
                    autoComplete="off"
                    spellCheck="false"
                  />
                </div>

                {/* Icon Grid */}
                <div className="h-36 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
                  <div className="grid grid-cols-8 gap-2">
                    {filteredIcons.map((icon, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => setSelectedIcon(icon)}
                        className={`h-10 w-10 rounded-lg border transition-colors flex items-center justify-center ${
                          selectedIcon === icon
                            ? "bg-primary/10 border-primary/20"
                            : "bg-card border-border hover:bg-muted/50"
                        }`}
                      >
                        {renderSpaceIcon(icon, "h-5 w-5")}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="text-sm text-destructive">
                  {error}
                </div>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="px-4 pb-4 pt-2 border-t">
            <Button
              type="submit"
              className="w-full"
              disabled={isSubmitting || !name.trim()}
            >
              {isSubmitting ? "Creating..." : "Create Space"}
            </Button>
          </div>
        </form>
      </MobileDialogContent>
    </MobileDialog>
  );
}
