"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

export type TaskStatus = "not_started" | "in_progress" | "completed";

interface StatusSquareProps {
  status: TaskStatus;
  size?: "xs" | "sm" | "md"; // xs ~ 12px, sm ~ 16px, md ~ 20px
  className?: string;
}

export function StatusSquare({ status, size = "md", className }: StatusSquareProps) {
  const dim = size === "xs" ? "h-3 w-3" : size === "sm" ? "h-4 w-4" : "h-5 w-5";
  const innerDim = size === "xs" ? "h-2 w-2" : size === "sm" ? "h-3 w-3" : "h-4 w-4";
  const halfDim = size === "xs" ? "h-2 w-[4px]" : size === "sm" ? "h-3 w-[6px]" : "h-4 w-[8px]"; // left half

  return (
    <span
      aria-hidden
      className={cn(
        // Match the glass styling of the multi-select radio circles
        "inline-flex items-center justify-start rounded-[3px] glass-radio-selector bg-transparent",
        dim,
        className
      )}
      data-testid="checkbox"
    >
      {status === "completed" ? (
        <span className={cn("block bg-primary glass-radio-dot", innerDim)} />
      ) : status === "in_progress" ? (
        <span
          className={cn("block bg-primary glass-radio-dot rounded-l-[2px]", halfDim)}
        />
      ) : null}
    </span>
  );
}

