"use client";

import { useMemo } from "react";
import { useUser } from "@stackframe/stack";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useSpaceCollaboratorsQuery, useSpaceQuery } from "@/lib/queries";
import type { SpaceCollaborator } from "@/lib/db";
import { Check, ChevronDown, UserX } from "lucide-react";

interface TaskAssigneeControlProps {
  spaceId?: string | null;
  assignedUserId?: string | null;
  onAssign: (userId: string | null) => void | Promise<void>;
  disabled?: boolean;
  size?: "sm" | "md";
  showLabel?: boolean; // When true, show avatar + name (modal); otherwise avatar-only (inline)
}

function getInitialsFromEmail(email?: string | null) {
  if (!email) return "?";
  const namePart = email.split("@")[0];
  const parts = namePart.split(".");
  if (parts.length >= 2) return (parts[0][0] + parts[1][0]).toUpperCase();
  return namePart.slice(0, 2).toUpperCase();
}

export function TaskAssigneeControl({
  spaceId,
  assignedUserId,
  onAssign,
  disabled = false,
  size = "sm",
  showLabel = false,
}: TaskAssigneeControlProps) {
  const user = useUser();
  const { data: space } = useSpaceQuery(spaceId || "");
  const { data: collaborators = [] } = useSpaceCollaboratorsQuery(spaceId || "");

  // Build list of selectable users: space owner + joined collaborators with user_id
  const options = useMemo(() => {
    const list: { id: string; label: string; email?: string | null; isOwner?: boolean }[] = [];
    if (space?.user_id) {
      const isSelf = user?.id === space.user_id;
      list.push({ id: space.user_id, label: isSelf ? (user?.displayName || "You") : "Owner", isOwner: true });
    }

    // Only include collaborators who have actually joined (user_id present)
    (collaborators as SpaceCollaborator[]).forEach((c) => {
      if (c.user_id) {
        const isSelf = user?.id === c.user_id;
        list.push({ id: c.user_id, label: isSelf ? (user?.displayName || "You") : (c.email || c.user_id), email: c.email });
      }
    });

    // Deduplicate by id (owner may also be collaborator)
    const seen = new Set<string>();
    return list.filter((o) => !seen.has(o.id) && seen.add(o.id));
  }, [space?.user_id, collaborators, user?.id, user?.displayName]);

  const currentOption = useMemo(() => options.find(o => o.id === assignedUserId) || null, [options, assignedUserId]);

  // Use larger size for better visibility
  const sizeClasses = "h-8 w-8 text-xs";

  const trigger = (
    <Button
      type="button"
      variant={showLabel ? "outline" : "ghost"}
      size="sm"
      disabled={disabled}
      className={`${showLabel ? "h-8 px-2" : "h-auto p-0"} inline-flex items-center gap-2`}
    >
      <Avatar className={`${sizeClasses}`}>
        {currentOption ? (
          <AvatarImage
            src={currentOption.id === user?.id ? (user?.avatarUrl || "") : ""}
            alt={currentOption.label}
          />
        ) : (
          <AvatarImage src="" alt="Unassigned" />
        )}
        <AvatarFallback>
          {currentOption
            ? (currentOption.email ? getInitialsFromEmail(currentOption.email) : currentOption.label.charAt(0))
            : ""}
        </AvatarFallback>
      </Avatar>
      {showLabel && (
        <span className="text-sm">
          {currentOption ? currentOption.label : "Unassigned"}
        </span>
      )}
      {showLabel && <ChevronDown className="h-4 w-4 opacity-70" />}
    </Button>
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {trigger}
      </DropdownMenuTrigger>
      <DropdownMenuContent align={showLabel ? "end" : "start"} className="z-[80] min-w-[220px]">
        <DropdownMenuItem
          onClick={(e) => { e.stopPropagation(); onAssign(null); }}
          className="flex items-center gap-2"
        >
          <UserX className="h-4 w-4" />
          <span>Unassign</span>
          {!currentOption && <Check className="h-4 w-4 ml-auto" />}
        </DropdownMenuItem>
        {options.map(opt => (
          <DropdownMenuItem
            key={opt.id}
            onClick={(e) => { e.stopPropagation(); onAssign(opt.id); }}
            className="flex items-center gap-2"
          >
            <Avatar className="h-6 w-6 text-[10px]">
              <AvatarImage src={opt.id === user?.id ? (user?.avatarUrl || "") : ""} alt={opt.label} />
              <AvatarFallback>{opt.email ? getInitialsFromEmail(opt.email) : opt.label.charAt(0)}</AvatarFallback>
            </Avatar>
            <span className="truncate">
              {opt.label}
            </span>
            {opt.id === assignedUserId && <Check className="h-4 w-4 ml-auto" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

