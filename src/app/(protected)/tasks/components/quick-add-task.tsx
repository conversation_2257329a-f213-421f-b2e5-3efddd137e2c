"use client";

import { useState, useRef, useEffect } from "react";
import { useUser } from "@stackframe/stack";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, Check, X } from "lucide-react";
import { useAddTaskMutation } from "@/lib/queries";
import { Task, TaskSortOption } from "@/lib/db";
import { useUsageBanner } from "@/components/ui/usage-banner";
import { getUsageLimitMessage } from "@/lib/usage-messages";

interface QuickAddTaskProps {
  onAddTaskClick: () => void;
  listId: string;
  sortOption: TaskSortOption;
  onTaskAdded: (newTask?: Task) => void;
  initialStatus?: 'not_started' | 'in_progress' | 'completed';
}

export function QuickAddTask({
  onAddTaskClick,
  listId,
  sortOption,
  onTaskAdded,
  initialStatus = 'not_started',
}: QuickAddTaskProps) {
  const user = useUser();
  const [isInlineMode, setIsInlineMode] = useState(false);
  const [newTaskTitle, setNewTaskTitle] = useState("");
  const newTaskInputRef = useRef<HTMLInputElement>(null);

  // TanStack Query mutation
  const addTaskMutation = useAddTaskMutation(listId, sortOption);

  // Usage banner hook
  const { showUsageBanner } = useUsageBanner();

  // Focus effect for new task input
  useEffect(() => {
    if (isInlineMode && newTaskInputRef.current) {
      newTaskInputRef.current.focus();
    }
  }, [isInlineMode]);

  // Inline task creation handler
  const handleAddTask = async () => {
    if (!user || !newTaskTitle.trim()) return;

    try {
      const result = await addTaskMutation.mutateAsync({
        userId: user.id,
        title: newTaskTitle.trim(),
        status: initialStatus,
      });

      if (result) {
        setNewTaskTitle("");
        setIsInlineMode(false);
        onTaskAdded(result);
      }
    } catch (error) {
      console.error('Error creating task:', error);

      // Check if it's a usage limit error
      if (error instanceof Error && error.message === 'USAGE_LIMIT_EXCEEDED') {
        showUsageBanner(getUsageLimitMessage('task'));
      }
    }
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Check if user is holding Shift key for modal mode
    if (e.shiftKey) {
      onAddTaskClick();
    } else {
      setIsInlineMode(true);
    }
  };

  if (isInlineMode) {
    return (
      <div className="flex items-center gap-2">
        <Input
          ref={newTaskInputRef}
          value={newTaskTitle}
          onChange={(e) => setNewTaskTitle(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleAddTask();
            } else if (e.key === 'Escape') {
              setNewTaskTitle("");
              setIsInlineMode(false);
            }
          }}
          placeholder="Enter task title..."
          className="h-8 text-sm"
          maxLength={200}
        />
        <Button
          variant="ghost"
          size="icon"
          onClick={handleAddTask}
          disabled={!newTaskTitle.trim()}
          className="h-8 w-8 p-0 shrink-0"
        >
          <Check className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            setNewTaskTitle("");
            setIsInlineMode(false);
          }}
          className="h-8 w-8 p-0 shrink-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <Card className="border-2 border-muted-foreground/20 bg-muted/10 hover:bg-muted/20 hover:border-muted-foreground/30 hover:scale-[1.02] transition-all duration-200 cursor-pointer" onClick={handleCardClick}>
      <CardContent className="px-2 flex items-center justify-center">
        <Plus className="h-5 w-5 text-muted-foreground" />
      </CardContent>
    </Card>
  );
}
