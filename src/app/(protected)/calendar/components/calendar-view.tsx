"use client";

import { Task } from "@/lib/db";
import {
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  addMonths,
  subMonths,
  startOfWeek,
  endOfWeek,
  addWeeks,
  subWeeks,
  isToday
} from "date-fns";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";
import { formatDate } from "@/lib/date-utils";

interface CalendarViewProps {
  tasks: Task[];
  viewType: "month" | "week";
  currentDate: Date;
  setCurrentDate: (date: Date) => void;
  weekStartsOn: "sunday" | "monday";
}

export function CalendarView({ tasks, viewType, currentDate, setCurrentDate, weekStartsOn }: CalendarViewProps) {
  // Convert weekStartsOn string to number (0 for Sunday, 1 for Monday)
  const weekStartsOnNumber = weekStartsOn === "monday" ? 1 : 0;
  const handlePrevious = () => {
    if (viewType === "month") {
      setCurrentDate(subMonths(currentDate, 1));
    } else {
      setCurrentDate(subWeeks(currentDate, 1));
    }
  };

  const handleNext = () => {
    if (viewType === "month") {
      setCurrentDate(addMonths(currentDate, 1));
    } else {
      setCurrentDate(addWeeks(currentDate, 1));
    }
  };

  const renderMonthView = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const startDate = startOfWeek(monthStart, { weekStartsOn: weekStartsOnNumber });
    const endDate = endOfWeek(monthEnd, { weekStartsOn: weekStartsOnNumber });

    const days = eachDayOfInterval({ start: startDate, end: endDate });

    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={handlePrevious}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-xl font-semibold">
              {formatDate(currentDate, "MMM yyyy")}
            </h2>
            <Button variant="outline" size="icon" onClick={handleNext}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <Button variant="outline" size="sm" onClick={() => setCurrentDate(new Date())}>
            Today
          </Button>
        </div>

        <div className="grid grid-cols-7 gap-0.5 text-center">
          {weekStartsOn === "monday"
            ? ["M", "T", "W", "Th", "F", "Sa", "S"].map((day, index) => (
                <div key={index} className="py-1 text-[8px] font-medium">
                  {day}
                </div>
              ))
            : ["S", "M", "T", "W", "Th", "F", "Sa"].map((day, index) => (
                <div key={index} className="py-1 text-[8px] font-medium">
                  {day}
                </div>
              ))
          }

          {days.map((day) => {
            const dayTasks = tasks.filter(task => {
              if (!task.due_date) return false;
              const taskDate = new Date(task.due_date);
              return isSameDay(taskDate, day);
            });

            const isCurrentMonth = isSameMonth(day, currentDate);

            return (
              <div
                key={day.toString()}
                className={`
                  aspect-square p-0.5 border border-border rounded-md flex flex-col
                  ${isCurrentMonth ? "" : "text-muted-foreground/50"}
                  ${isToday(day) ? "bg-primary/10 border-primary" : ""}
                `}
              >
                <div className="text-right text-[8px] p-0.5">
                  {formatDate(day, "d")}
                </div>
                <div className="flex flex-wrap gap-[1px] justify-center mt-auto mb-0.5">
                  {dayTasks.slice(0, 6).map((task) => (
                    <Link
                      key={task.id}
                      href="/tasks"
                      className={`
                        w-1 h-1 rounded-full
                        ${task.status === "completed" ? "bg-muted-foreground/50" : "bg-primary"}
                      `}
                      title={task.title}
                    />
                  ))}
                  {dayTasks.length > 6 && (
                    <div className="w-1 h-1 rounded-full bg-primary/50 relative" title={`${dayTasks.length - 6} more tasks`} />
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderWeekView = () => {
    const weekStart = startOfWeek(currentDate, { weekStartsOn: weekStartsOnNumber });
    const weekEnd = endOfWeek(currentDate, { weekStartsOn: weekStartsOnNumber });
    const days = eachDayOfInterval({ start: weekStart, end: weekEnd });

    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={handlePrevious}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-xl font-semibold">
              {formatDate(weekStart, "MMM d")} - {formatDate(weekEnd, "MMM d, yyyy")}
            </h2>
            <Button variant="outline" size="icon" onClick={handleNext}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <Button variant="outline" size="sm" onClick={() => setCurrentDate(new Date())}>
            Today
          </Button>
        </div>

        <div className="space-y-2">
          {days.map((day) => {
            const dayTasks = tasks.filter(task => {
              if (!task.due_date) return false;
              const taskDate = new Date(task.due_date);
              return isSameDay(taskDate, day);
            });

            return (
              <div
                key={day.toString()}
                className={`
                  p-3 border border-border rounded-md
                  ${isToday(day) ? "bg-primary/10 border-primary" : ""}
                `}
              >
                <div className="font-medium mb-2">
                  {formatDate(day, "EEEE, MMM d")}
                </div>
                {dayTasks.length === 0 ? (
                  <div className="text-sm text-muted-foreground py-2">
                    No tasks due
                  </div>
                ) : (
                  <div className="flex flex-wrap gap-2">
                    {dayTasks.map((task) => (
                      <Link
                        key={task.id}
                        href="/tasks"
                        className="flex items-center gap-2 py-1"
                      >
                        <div className={`
                          w-2 h-2 rounded-full
                          ${task.status === "completed" ? "bg-muted-foreground/50" : "bg-primary"}
                        `} />
                        <span className={`text-sm truncate max-w-[180px] sm:max-w-[240px] md:max-w-[280px] inline-block ${task.status === "completed" ? "line-through text-muted-foreground" : ""}`} title={task.title}>
                          {task.title}
                        </span>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return viewType === "month" ? renderMonthView() : renderWeekView();
}
