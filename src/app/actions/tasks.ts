"use server";

import { unstable_cache, revalidateTag, revalidatePath } from "next/cache";
import { Task, TaskSortOption, createTask, createSubtask, deleteTask, deleteCompletedTasksByUserId, getTaskById, getTasksByUserId, getTasksByListId, getTasksWithSubtasksByListId, getTasksByTagId, getSubtasksByParentId, getUpcomingTasksByDueDate, getUpcomingTasksWithListsByDueDate, reorderTasks, updateTask, setTaskTags, duplicateTaskWithTags, moveTaskToList as moveTaskToListDb, getIncompleteTasksByListId, getCompletedTasksByListIdPaginated, getCompletedTaskCountByListId, getIncompleteSubtasksByParentId, getCompletedSubtasksByParentIdPaginated, getCompletedSubtasksCountByParentId } from "@/lib/db";
import { TaskWithList } from "@/lib/types";

export const fetchTasks = unstable_cache(
  async (userId: string, sortOption: TaskSortOption = "position"): Promise<Task[]> => {
    const tasks = await getTasksByUserId(userId, sortOption);
    return tasks;
  },
  ["tasks-jwt"],
  {
    tags: ["tasks"],
    revalidate: 600, // 10 minutes
  }
);

export const fetchTasksByList = unstable_cache(
  async (listId: string, sortOption: TaskSortOption = "position", requestingUserId?: string): Promise<Task[]> => {
    const tasks = await getTasksByListId(listId, sortOption, requestingUserId);
    return tasks;
  },
  ["tasks-by-list-jwt"],
  {
    tags: ["tasks", "lists"],
    revalidate: 600, // 10 minutes
  }
);

// New: fetch incomplete tasks by list (no completed)
export const fetchIncompleteTasksByList = unstable_cache(
  async (listId: string, sortOption: TaskSortOption = "position", requestingUserId?: string): Promise<Task[]> => {
    const tasks = await getIncompleteTasksByListId(listId, sortOption, requestingUserId);
    return tasks;
  },
  ["incomplete-tasks-by-list-jwt"],
  {
    tags: ["tasks", "lists"],
    revalidate: 600,
  }
);

// New: fetch completed tasks by list with pagination
export async function fetchCompletedTasksByListPaginated(
  listId: string,
  sortOption: TaskSortOption = "position",
  limit: number = 10,
  offset: number = 0,
  requestingUserId?: string
): Promise<Task[]> {
  return getCompletedTasksByListIdPaginated(listId, sortOption, limit, offset, requestingUserId);
}

// New: fetch completed task count for a list
export const fetchCompletedTaskCountByList = unstable_cache(
  async (listId: string, requestingUserId?: string): Promise<number> => {
    const count = await getCompletedTaskCountByListId(listId, requestingUserId);
    return count;
  },
  ["completed-task-count-by-list-jwt"],
  {
    tags: ["tasks", "lists"],
    revalidate: 600,
  }
);


export async function fetchTask(taskId: string): Promise<Task | null> {
  return getTaskById(taskId);
}

export const fetchTasksByTag = unstable_cache(
  async (tagId: string, userId: string, sortOption: TaskSortOption = "position"): Promise<Task[]> => {
    console.log("Server action: fetchTasksByTag called with tagId:", tagId, "userId:", userId);
    const tasks = await getTasksByTagId(tagId, userId, sortOption);
    console.log("Server action: fetchTasksByTag returning:", tasks);
    return tasks;
  },
  ["tasks-by-tag-jwt"],
  {
    tags: ["tasks", "tags"],
    revalidate: 600, // 10 minutes
  }
);

export async function addTask(
  userId: string,
  listId: string,
  data: {
    title: string;
    description?: string;
    due_date?: Date;
    status?: string;
    tagIds?: string[];
    parent_task_id?: string;
  }
): Promise<Task | null> {
  console.log("Server action: addTask called with userId:", userId, "listId:", listId, "data:", data);

  const { tagIds, ...taskData } = data;
  const task = await createTask(userId, listId, taskData);

  if (task && tagIds && tagIds.length > 0) {
    // Associate tags with the task
    await setTaskTags(task.id, tagIds, userId);
  }

  // Invalidate cache when task is added
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: addTask created task:", task);
  return task;
}

// Subtask-specific server actions
export async function addSubtask(
  userId: string,
  parentTaskId: string,
  data: {
    title: string;
    description?: string;
    due_date?: Date;
    status?: string;
    tagIds?: string[];
  }
): Promise<Task | null> {
  console.log("Server action: addSubtask called with userId:", userId, "parentTaskId:", parentTaskId, "data:", data);

  const { tagIds, ...subtaskData } = data;
  const subtask = await createSubtask(userId, parentTaskId, subtaskData);

  if (subtask && tagIds && tagIds.length > 0) {
    // Associate tags with the subtask
    await setTaskTags(subtask.id, tagIds, userId);
  }

  // Invalidate cache when subtask is added
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: addSubtask created subtask:", subtask);
  return subtask;
}

export async function fetchSubtasks(parentTaskId: string, userId: string): Promise<Task[]> {
  const subtasks = await getSubtasksByParentId(parentTaskId, userId);
  return subtasks;
}

// New: Subtasks specialized fetchers
export async function fetchIncompleteSubtasks(parentTaskId: string, userId: string) {
  return getIncompleteSubtasksByParentId(parentTaskId, userId);
}

export async function fetchCompletedSubtasksPaginated(parentTaskId: string, userId: string, limit: number = 10, offset: number = 0) {
  return getCompletedSubtasksByParentIdPaginated(parentTaskId, limit, offset, userId);
}

export async function fetchCompletedSubtasksCount(parentTaskId: string, userId: string) {
  return getCompletedSubtasksCountByParentId(parentTaskId, userId);
}

export const fetchTasksWithSubtasks = unstable_cache(
  async (listId: string, sortOption: TaskSortOption = "position", requestingUserId?: string): Promise<Task[]> => {
    const tasks = await getTasksWithSubtasksByListId(listId, sortOption, requestingUserId);
    return tasks;
  },
  ["tasks-with-subtasks-jwt"],
  {
    tags: ["tasks", "lists"],
    revalidate: 600, // 10 minutes
  }
);

export async function editTask(
  taskId: string,
  userId: string,
  data: Partial<Omit<Task, "id" | "user_id" | "created_at" | "updated_at">>
): Promise<Task | null> {
  const result = await updateTask(taskId, userId, data);

  // Selective invalidation based on what changed
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views

  // Only invalidate task counts if status changed (affects completed/active counts)
  if (data.status) {
    revalidateTag("lists");
  }

  return result;
}

export async function removeTask(taskId: string, userId: string): Promise<boolean> {
  const result = await deleteTask(taskId, userId);

  // Invalidate cache when task is deleted
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  return result;
}

export async function duplicateTask(taskId: string, userId: string): Promise<Task | null> {
  console.log("Server action: duplicateTask called with taskId:", taskId, "userId:", userId);

  const duplicatedTask = await duplicateTaskWithTags(taskId, userId);

  // Invalidate cache when task is duplicated
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: duplicateTask created task:", duplicatedTask);
  return duplicatedTask;
}

export async function archiveTask(taskId: string, userId: string): Promise<Task | null> {
  const { archiveTask: archiveTaskDb } = await import("@/lib/db");
  const result = await archiveTaskDb(taskId, userId);
  revalidateTag("tasks");
  revalidateTag("tags");
  revalidateTag("lists");
  return result;
}

export async function unarchiveTask(taskId: string, userId: string): Promise<Task | null> {
  const { unarchiveTask: unarchiveTaskDb } = await import("@/lib/db");
  const result = await unarchiveTaskDb(taskId, userId);
  revalidateTag("tasks");
  revalidateTag("tags");
  revalidateTag("lists");
  return result;
}


export async function updateTaskOrder(userId: string, taskIds: string[]): Promise<boolean> {
  const result = await reorderTasks(userId, taskIds);

  // Invalidate cache when task order is updated
  revalidateTag("tasks");

  return result;
}

export async function moveTaskToList(
  taskId: string,
  userId: string,
  newListId: string
): Promise<Task | null> {
  console.log("Server action: moveTaskToList called with taskId:", taskId, "userId:", userId, "newListId:", newListId);

  const result = await moveTaskToListDb(taskId, userId, newListId);

  // Invalidate cache when task is moved between lists
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: moveTaskToList returning:", result);
  return result;
}

export const fetchUpcomingTasks = unstable_cache(
  async (userId: string): Promise<TaskWithList[]> => {
    const tasks = await getUpcomingTasksWithListsByDueDate(userId);
    return tasks as TaskWithList[];
  },
  ["upcoming-tasks-jwt"],
  {
    tags: ["tasks"],
    revalidate: 600, // 10 minutes
  }
);

export async function deleteCompletedTasks(userId: string): Promise<number> {
  console.log("Server action: deleteCompletedTasks called with userId:", userId);
  const deletedCount = await deleteCompletedTasksByUserId(userId);

  // Invalidate cache when completed tasks are deleted
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: deleteCompletedTasks deleted count:", deletedCount);
  return deletedCount;
}

// Bulk operations for selection mode
export async function bulkRemoveTasks(taskIds: string[], userId: string): Promise<number> {
  console.log("Server action: bulkRemoveTasks called with taskIds:", taskIds, "userId:", userId);

  if (taskIds.length === 0) return 0;

  const { bulkDeleteTasks } = await import("@/lib/db");
  const deletedCount = await bulkDeleteTasks(taskIds, userId);

  // Invalidate cache when tasks are deleted
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: bulkRemoveTasks deleted count:", deletedCount);
  return deletedCount;
}

export async function bulkDuplicateTasks(taskIds: string[], userId: string, listId: string): Promise<Task[]> {
  console.log("Server action: bulkDuplicateTasks called with taskIds:", taskIds, "userId:", userId, "listId:", listId);

  if (taskIds.length === 0) return [];

  const { bulkDuplicateTasksWithTags } = await import("@/lib/db");
  const duplicatedTasks = await bulkDuplicateTasksWithTags(taskIds, userId, listId);

  // Invalidate cache when tasks are duplicated
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: bulkDuplicateTasks created tasks:", duplicatedTasks);
  return duplicatedTasks;
}

export async function bulkMoveTasksToList(taskIds: string[], userId: string, newListId: string): Promise<Task[]> {
  console.log("Server action: bulkMoveTasksToList called with taskIds:", taskIds, "userId:", userId, "newListId:", newListId);

  if (taskIds.length === 0) return [];

  const { bulkMoveTasksToListDb } = await import("@/lib/db");
  const movedTasks = await bulkMoveTasksToListDb(taskIds, userId, newListId);

  // Invalidate cache when tasks are moved between lists
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: bulkMoveTasksToList moved tasks:", movedTasks);
  return movedTasks;
}

export async function bulkAddTagToTasks(taskIds: string[], userId: string, tagId: string): Promise<boolean> {
  console.log("Server action: bulkAddTagToTasks called with taskIds:", taskIds, "userId:", userId, "tagId:", tagId);

  if (taskIds.length === 0) return false;

  const { bulkAddTagToTasksDb } = await import("@/lib/db");
  const success = await bulkAddTagToTasksDb(taskIds, userId, tagId);

  // Invalidate cache when tags are added to tasks
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: bulkAddTagToTasks success:", success);
  return success;
}


export async function bulkArchiveTasks(taskIds: string[], userId: string): Promise<number> {
  if (taskIds.length === 0) return 0;
  const { bulkArchiveTasks: bulkArchiveTasksDb } = await import("@/lib/db");
  const count = await bulkArchiveTasksDb(taskIds, userId);
  revalidateTag("tasks");
  revalidateTag("tags");
  revalidateTag("lists");
  return count;
}

export async function bulkUnarchiveTasks(taskIds: string[], userId: string): Promise<number> {
  if (taskIds.length === 0) return 0;
  const { bulkUnarchiveTasks: bulkUnarchiveTasksDb } = await import("@/lib/db");
  const count = await bulkUnarchiveTasksDb(taskIds, userId);
  revalidateTag("tasks");
  revalidateTag("tags");
  revalidateTag("lists");
  return count;
}



export async function bulkMarkTasksComplete(taskIds: string[], userId: string): Promise<Task[]> {
  console.log("Server action: bulkMarkTasksComplete called with taskIds:", taskIds, "userId:", userId);

  if (taskIds.length === 0) return [];

  const { bulkUpdateTasksStatus } = await import("@/lib/db");
  const updatedTasks = await bulkUpdateTasksStatus(taskIds, userId, 'completed');

  // Invalidate cache when task statuses are updated
  revalidateTag("tasks");
  revalidateTag("tags"); // For tag-filtered views
  revalidateTag("lists"); // For task counts

  console.log("Server action: bulkMarkTasksComplete updated tasks:", updatedTasks);
  return updatedTasks;
}
