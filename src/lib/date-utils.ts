import { format, isValid, parse, isToday, isTomorrow, addDays, setHours, setMinutes } from "date-fns";

/**
 * Format a date with a consistent format across the application
 * @param date The date to format
 * @param formatStr The format string to use (defaults to "MMM d, yyyy")
 * @returns The formatted date string
 */
export function formatDate(date: Date | string | null | undefined, formatStr: string = "MMM d, yyyy"): string {
  if (!date) return "";

  const dateObj = typeof date === "string" ? new Date(date) : date;

  if (!isValid(dateObj)) return "";

  return format(dateObj, formatStr);
}

/**
 * Format a datetime with both date and time
 * @param date The date to format
 * @param includeTime Whether to include time in the format
 * @returns The formatted datetime string
 */
export function formatDateTime(date: Date | string | null | undefined, includeTime: boolean = true): string {
  if (!date) return "";

  const dateObj = typeof date === "string" ? new Date(date) : date;

  if (!isValid(dateObj)) return "";

  if (!includeTime) {
    return format(dateObj, "MMM d, yyyy");
  }

  // Check if the time is midnight (00:00)
  const hours = dateObj.getHours();
  const minutes = dateObj.getMinutes();

  if (hours === 0 && minutes === 0) {
    // If it's midnight, just show the date
    return format(dateObj, "MMM d, yyyy");
  }

  return format(dateObj, "MMM d, yyyy 'at' p");
}

/**
 * Format time only from a date
 * @param date The date to extract time from
 * @returns The formatted time string
 */
export function formatTime(date: Date | string | null | undefined): string {
  if (!date) return "";

  const dateObj = typeof date === "string" ? new Date(date) : date;

  if (!isValid(dateObj)) return "";

  return format(dateObj, "p");
}

/**
 * Check if a date has a time component (not midnight)
 * @param date The date to check
 * @returns True if the date has a time component, false otherwise
 */
export function hasTimeComponent(date: Date | string | null | undefined): boolean {
  if (!date) return false;

  const dateObj = typeof date === "string" ? new Date(date) : date;

  if (!isValid(dateObj)) return false;

  return dateObj.getHours() !== 0 || dateObj.getMinutes() !== 0;
}

/**
 * Set time on a date object
 * @param date The date to set time on
 * @param hours The hours (0-23)
 * @param minutes The minutes (0-59)
 * @returns A new date with the time set
 */
export function setTimeOnDate(date: Date, hours: number, minutes: number): Date {
  return setMinutes(setHours(date, hours), minutes);
}

/**
 * Clear time from a date (set to midnight)
 * @param date The date to clear time from
 * @returns A new date with time set to midnight
 */
export function clearTimeFromDate(date: Date): Date {
  return setTimeOnDate(date, 0, 0);
}

/**
 * Parse a date string into a Date object
 * @param dateStr The date string to parse
 * @param formatStr The format string to use (defaults to "yyyy-MM-dd")
 * @returns The parsed Date object or undefined if invalid
 */
export function parseDate(dateStr: string, formatStr: string = "yyyy-MM-dd"): Date | undefined {
  if (!dateStr) return undefined;

  const parsedDate = parse(dateStr, formatStr, new Date());

  if (!isValid(parsedDate)) return undefined;

  return parsedDate;
}

/**
 * Get a human-readable relative date string
 * @param date The date to format
 * @param includeTime Whether to include time in the relative string
 * @returns A human-readable string like "Today", "Tomorrow", or the formatted date
 */
export function getRelativeDateString(date: Date | string | null | undefined, includeTime: boolean = false): string {
  if (!date) return "";

  const dateObj = typeof date === "string" ? new Date(date) : date;

  if (!isValid(dateObj)) return "";

  const timeStr = includeTime && hasTimeComponent(dateObj) ? ` at ${formatTime(dateObj)}` : "";

  if (isToday(dateObj)) return `Today${timeStr}`;
  if (isTomorrow(dateObj)) return `Tomorrow${timeStr}`;

  return includeTime && hasTimeComponent(dateObj)
    ? formatDateTime(dateObj, true)
    : formatDate(dateObj, "MMM d");
}

/**
 * Check if a date is valid
 * @param date The date to check
 * @returns True if the date is valid, false otherwise
 */
export function isValidDate(date: any): boolean {
  if (!date) return false;

  const dateObj = typeof date === "string" ? new Date(date) : date;

  return isValid(dateObj);
}

/**
 * Get the start of today
 * @returns The start of today
 */
export function getStartOfToday(): Date {
  return new Date(new Date().setHours(0, 0, 0, 0));
}

/**
 * Get tomorrow's date
 * @returns Tomorrow's date
 */
export function getTomorrow(): Date {
  return addDays(getStartOfToday(), 1);
}

/**
 * Check if a date is past due (before today)
 * @param date The date to check
 * @returns True if the date is before today, false otherwise. Returns false if the date is invalid.
 */
export function isPastDue(date: Date | string | null | undefined): boolean {
  if (!date) return false;

  const dateObj = typeof date === "string" ? new Date(date) : date;

  if (!isValid(dateObj)) return false;

  const today = getStartOfToday();
  return dateObj < today;
}
