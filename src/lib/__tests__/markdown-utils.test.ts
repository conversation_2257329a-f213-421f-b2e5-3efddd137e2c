/**
 * @jest-environment jsdom
 */

import {
  markdownToHtml,
  htmlToMarkdown,
  normalizeMarkdown,
  isMarkdown,
  htmlFromExternalToMarkdown,
  htmlToPlainText,
} from '../markdown-utils';

describe('Markdown Utilities', () => {
  describe('markdownToHtml', () => {
    it('converts basic markdown to HTML', () => {
      const markdown = '**bold** and *italic*';
      const html = markdownToHtml(markdown);
      expect(html).toContain('<strong>bold</strong>');
      expect(html).toContain('<em>italic</em>');
    });

    it('handles headers', () => {
      const markdown = '# Header 1\n## Header 2\n### Header 3';
      const html = markdownToHtml(markdown);
      expect(html).toContain('<h1>Header 1</h1>');
      expect(html).toContain('<h2>Header 2</h2>');
      expect(html).toContain('<h3>Header 3</h3>');
    });

    it('handles bullet lists', () => {
      const markdown = '- Item 1\n- Item 2\n- Item 3';
      const html = markdownToHtml(markdown);
      expect(html).toContain('<ul>');
      expect(html).toContain('<li>Item 1</li>');
      expect(html).toContain('<li>Item 2</li>');
      expect(html).toContain('<li>Item 3</li>');
      expect(html).toContain('</ul>');
    });

    it('handles numbered lists', () => {
      const markdown = '1. First\n2. Second\n3. Third';
      const html = markdownToHtml(markdown);
      expect(html).toContain('<ol>');
      expect(html).toContain('<li>First</li>');
      expect(html).toContain('<li>Second</li>');
      expect(html).toContain('<li>Third</li>');
      expect(html).toContain('</ol>');
    });

    it('handles code blocks', () => {
      const markdown = '```javascript\nconst x = 1;\n```';
      const html = markdownToHtml(markdown);
      expect(html).toContain('<pre><code class="language-javascript">');
      expect(html).toContain('const x = 1;');
      expect(html).toContain('</code></pre>');
    });

    it('handles inline code', () => {
      const markdown = 'This is `inline code` in text';
      const html = markdownToHtml(markdown);
      expect(html).toContain('<code>inline code</code>');
    });

    it('handles links', () => {
      const markdown = '[Link text](https://example.com)';
      const html = markdownToHtml(markdown);
      expect(html).toContain('<a href="https://example.com">Link text</a>');
    });

    it('handles strikethrough', () => {
      const markdown = '~~strikethrough text~~';
      const html = markdownToHtml(markdown);
      expect(html).toContain('<del>strikethrough text</del>');
    });

    it('returns default paragraph for empty input', () => {
      expect(markdownToHtml('')).toBe('<p></p>');
      expect(markdownToHtml('   ')).toBe('<p></p>');
    });
  });

  describe('htmlToMarkdown', () => {
    it('converts basic HTML to markdown', () => {
      const html = '<p><strong>bold</strong> and <em>italic</em></p>';
      const markdown = htmlToMarkdown(html);
      expect(markdown).toContain('**bold**');
      expect(markdown).toContain('*italic*');
    });

    it('handles headers', () => {
      const html = '<h1>Header 1</h1><h2>Header 2</h2>';
      const markdown = htmlToMarkdown(html);
      expect(markdown).toContain('# Header 1');
      expect(markdown).toContain('## Header 2');
    });

    it('handles lists', () => {
      const html = '<ul><li>Item 1</li><li>Item 2</li></ul>';
      const markdown = htmlToMarkdown(html);
      expect(markdown).toContain('- Item 1');
      expect(markdown).toContain('- Item 2');
    });

    it('handles numbered lists', () => {
      const html = '<ol><li>First</li><li>Second</li></ol>';
      const markdown = htmlToMarkdown(html);
      expect(markdown).toContain('1. First');
      expect(markdown).toContain('2. Second');
    });

    it('handles code blocks', () => {
      const html = '<pre><code class="language-javascript">const x = 1;</code></pre>';
      const markdown = htmlToMarkdown(html);
      expect(markdown).toContain('```javascript');
      expect(markdown).toContain('const x = 1;');
      expect(markdown).toContain('```');
    });

    it('handles inline code', () => {
      const html = '<p>This is <code>inline code</code></p>';
      const markdown = htmlToMarkdown(html);
      expect(markdown).toContain('`inline code`');
    });

    it('handles links', () => {
      const html = '<a href="https://example.com">Link text</a>';
      const markdown = htmlToMarkdown(html);
      expect(markdown).toContain('[Link text](https://example.com)');
    });

    it('handles strikethrough', () => {
      const html = '<del>strikethrough</del>';
      const markdown = htmlToMarkdown(html);
      expect(markdown).toContain('~~strikethrough~~');
    });

    it('returns empty string for empty HTML', () => {
      expect(htmlToMarkdown('')).toBe('');
      expect(htmlToMarkdown('<p></p>')).toBe('');
      expect(htmlToMarkdown('<p><br></p>')).toBe('');
    });
  });

  describe('normalizeMarkdown', () => {
    it('normalizes line endings', () => {
      const markdown = 'line1\r\nline2\rline3\nline4';
      const normalized = normalizeMarkdown(markdown);
      expect(normalized).toBe('line1\nline2\nline3\nline4');
    });

    it('removes trailing spaces', () => {
      const markdown = 'line with spaces   \nanother line  ';
      const normalized = normalizeMarkdown(markdown);
      expect(normalized).toBe('line with spaces\nanother line');
    });

    it('limits consecutive newlines', () => {
      const markdown = 'paragraph1\n\n\n\nparagraph2';
      const normalized = normalizeMarkdown(markdown);
      expect(normalized).toBe('paragraph1\n\nparagraph2');
    });

    it('standardizes bullet points', () => {
      const markdown = '* Item 1\n+ Item 2\n- Item 3';
      const normalized = normalizeMarkdown(markdown);
      expect(normalized).toContain('- Item 1');
      expect(normalized).toContain('- Item 2');
      expect(normalized).toContain('- Item 3');
    });

    it('normalizes numbered lists', () => {
      const markdown = '1.Item 1\n2.  Item 2\n3.   Item 3';
      const normalized = normalizeMarkdown(markdown);
      expect(normalized).toContain('1. Item 1');
      expect(normalized).toContain('2. Item 2');
      expect(normalized).toContain('3. Item 3');
    });

    it('ensures proper header spacing', () => {
      const markdown = '#Header\n##  Header 2\n###Header 3';
      const normalized = normalizeMarkdown(markdown);
      expect(normalized).toContain('# Header');
      expect(normalized).toContain('## Header 2');
      expect(normalized).toContain('### Header 3');
    });
  });

  describe('isMarkdown', () => {
    it('detects headers', () => {
      expect(isMarkdown('# Header')).toBe(true);
      expect(isMarkdown('## Header 2')).toBe(true);
      expect(isMarkdown('### Header 3')).toBe(true);
    });

    it('detects lists', () => {
      expect(isMarkdown('- List item')).toBe(true);
      expect(isMarkdown('* List item')).toBe(true);
      expect(isMarkdown('+ List item')).toBe(true);
      expect(isMarkdown('1. Numbered item')).toBe(true);
    });

    it('detects formatting', () => {
      expect(isMarkdown('**bold text**')).toBe(true);
      expect(isMarkdown('*italic text*')).toBe(true);
      expect(isMarkdown('`inline code`')).toBe(true);
      expect(isMarkdown('~~strikethrough~~')).toBe(true);
    });

    it('detects code blocks', () => {
      expect(isMarkdown('```\ncode\n```')).toBe(true);
      expect(isMarkdown('```javascript')).toBe(true);
    });

    it('detects links', () => {
      expect(isMarkdown('[link](url)')).toBe(true);
    });

    it('returns false for plain text', () => {
      expect(isMarkdown('Just plain text')).toBe(false);
      expect(isMarkdown('No special formatting here')).toBe(false);
    });
  });

  describe('htmlFromExternalToMarkdown', () => {
    it('removes unwanted HTML elements', () => {
      const html = '<script>alert("bad")</script><p>Good content</p><style>.bad{}</style>';
      const markdown = htmlFromExternalToMarkdown(html);
      expect(markdown).not.toContain('<script>');
      expect(markdown).not.toContain('<style>');
      expect(markdown).toContain('Good content');
    });

    it('removes class and style attributes', () => {
      const html = '<p class="fancy" style="color: red;">Content</p>';
      const markdown = htmlFromExternalToMarkdown(html);
      expect(markdown).toContain('Content');
      expect(markdown).not.toContain('class=');
      expect(markdown).not.toContain('style=');
    });

    it('converts divs to paragraphs', () => {
      const html = '<div>Content in div</div>';
      const markdown = htmlFromExternalToMarkdown(html);
      expect(markdown).toContain('Content in div');
    });
  });

  describe('htmlToPlainText', () => {
    beforeEach(() => {
      // Mock document.createElement for Node.js environment
      global.document = {
        createElement: jest.fn(() => ({
          innerHTML: '',
          textContent: 'Plain text content',
          innerText: 'Plain text content',
          querySelectorAll: jest.fn(() => []),
        })),
      } as any;
    });

    it('extracts plain text from HTML', () => {
      const html = '<p><strong>Bold</strong> and <em>italic</em> text</p>';
      const text = htmlToPlainText(html);
      expect(text).toBe('Plain text content');
    });

    it('handles empty HTML', () => {
      const text = htmlToPlainText('');
      expect(text).toBe('Plain text content');
    });
  });
});
