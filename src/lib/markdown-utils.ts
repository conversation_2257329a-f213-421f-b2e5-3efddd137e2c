/**
 * Professional markdown utilities for the rich text editor
 * Provides robust conversion between HTML and Markdown with support for all standard syntax
 */

export interface MarkdownParseOptions {
  preserveWhitespace?: boolean;
  allowHtml?: boolean;
}

/**
 * Convert markdown to HTML with proper handling of all standard markdown syntax
 */
export function markdownToHtml(markdown: string, options: MarkdownParseOptions = {}): string {
  if (!markdown?.trim()) return '<p></p>';

  const lines = markdown.split('\n');
  const result: string[] = [];
  let inCodeBlock = false;
  let codeBlockLanguage = '';
  let inList = false;
  let listType = '';
  let listLevel = 0;
  let listStack: Array<{ type: string; level: number }> = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // Handle code blocks
    const codeBlockMatch = line.match(/^```(\w+)?/);
    if (codeBlockMatch) {
      if (!inCodeBlock) {
        inCodeBlock = true;
        codeBlockLanguage = codeBlockMatch[1] || '';
        result.push(`<pre><code${codeBlockLanguage ? ` class="language-${codeBlockLanguage}"` : ''}>`);
      } else {
        inCodeBlock = false;
        codeBlockLanguage = '';
        result.push('</code></pre>');
      }
      continue;
    }

    if (inCodeBlock) {
      result.push(escapeHtml(line));
      continue;
    }

    // Handle headers
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);
    if (headerMatch) {
      closeOpenLists();
      const level = headerMatch[1].length;
      const content = processInlineFormatting(headerMatch[2]);
      result.push(`<h${level}>${content}</h${level}>`);
      continue;
    }

    // Handle lists with proper nesting
    const listMatch = line.match(/^(\s*)([*\-+]|\d+\.)\s+(.+)$/);
    if (listMatch) {
      const indent = listMatch[1].length;
      const marker = listMatch[2];
      const content = processInlineFormatting(listMatch[3]);
      const currentListType = /^\d+\./.test(marker) ? 'ol' : 'ul';
      const currentLevel = Math.floor(indent / 2);

      // Handle list nesting
      if (!inList || currentLevel !== listLevel || currentListType !== listType) {
        if (currentLevel > listLevel) {
          // Starting a nested list
          for (let level = listLevel; level < currentLevel; level++) {
            result.push(`<${currentListType}>`);
            listStack.push({ type: currentListType, level: level + 1 });
          }
        } else if (currentLevel < listLevel) {
          // Closing nested lists
          while (listStack.length > 0 && listStack[listStack.length - 1].level > currentLevel) {
            const lastList = listStack.pop()!;
            result.push(`</${lastList.type}>`);
          }
        } else if (currentListType !== listType) {
          // Changing list type at same level
          if (inList) result.push(`</${listType}>`);
          result.push(`<${currentListType}>`);
          if (listStack.length > 0) {
            listStack[listStack.length - 1] = { type: currentListType, level: currentLevel };
          }
        }

        if (!inList) {
          result.push(`<${currentListType}>`);
          listStack.push({ type: currentListType, level: currentLevel });
        }

        inList = true;
        listType = currentListType;
        listLevel = currentLevel;
      }

      result.push(`<li>${content}</li>`);
      continue;
    }

    // Close lists if we're not in a list item
    if (inList && trimmedLine) {
      closeOpenLists();
    }

    // Handle empty lines
    if (!trimmedLine) {
      if (inList) {
        // Empty line in list - just continue
        continue;
      } else {
        result.push('<p></p>');
      }
      continue;
    }

    // Handle regular paragraphs
    if (!inList) {
      const content = processInlineFormatting(line);
      result.push(`<p>${content}</p>`);
    }
  }

  // Close any remaining open lists
  closeOpenLists();

  function closeOpenLists() {
    while (listStack.length > 0) {
      const list = listStack.pop()!;
      result.push(`</${list.type}>`);
    }
    inList = false;
    listLevel = 0;
    listType = '';
  }

  return result.join('') || '<p></p>';
}

/**
 * Process inline formatting (bold, italic, code, links, etc.)
 */
function processInlineFormatting(text: string): string {
  return text
    // Links: [text](url)
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
    // Inline code: `code`
    .replace(/`([^`]+)`/g, '<code>$1</code>')
    // Bold: **text** or __text__
    .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
    .replace(/__([^_]+)__/g, '<strong>$1</strong>')
    // Italic: *text* or _text_ (but not if surrounded by word characters)
    .replace(/(?<!\w)\*([^*]+)\*(?!\w)/g, '<em>$1</em>')
    .replace(/(?<!\w)_([^_]+)_(?!\w)/g, '<em>$1</em>')
    // Strikethrough: ~~text~~
    .replace(/~~([^~]+)~~/g, '<del>$1</del>');
}

/**
 * Convert HTML to markdown with proper handling of all elements
 */
export function htmlToMarkdown(html: string): string {
  if (!html || html === '<p></p>' || html === '<p><br></p>') return '';

  let markdown = html;

  // Handle code blocks first
  markdown = markdown.replace(/<pre><code(?:\s+class="language-(\w+)")?>(.*?)<\/code><\/pre>/gs, (match, lang, content) => {
    const cleanContent = content.replace(/<br\s*\/?>/g, '\n').trim();
    return `\`\`\`${lang || ''}\n${cleanContent}\n\`\`\`\n`;
  });

  // Handle headers
  for (let i = 1; i <= 6; i++) {
    const headerRegex = new RegExp(`<h${i}[^>]*>(.*?)<\/h${i}>`, 'g');
    markdown = markdown.replace(headerRegex, `${'#'.repeat(i)} $1\n`);
  }

  // Handle lists with proper nesting
  markdown = processLists(markdown);

  // Handle inline formatting
  markdown = markdown
    // Links
    .replace(/<a[^>]+href="([^"]*)"[^>]*>(.*?)<\/a>/g, '[$2]($1)')
    // Inline code
    .replace(/<code[^>]*>(.*?)<\/code>/g, '`$1`')
    // Bold
    .replace(/<strong[^>]*>(.*?)<\/strong>/g, '**$1**')
    // Italic
    .replace(/<em[^>]*>(.*?)<\/em>/g, '*$1*')
    // Strikethrough
    .replace(/<del[^>]*>(.*?)<\/del>/g, '~~$1~~')
    // Paragraphs
    .replace(/<p[^>]*>(.*?)<\/p>/g, '$1\n')
    // Line breaks
    .replace(/<br\s*\/?>/g, '\n');

  // Clean up extra whitespace and newlines
  markdown = markdown
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Multiple newlines to double newlines
    .replace(/^\s+|\s+$/g, '') // Trim start and end
    .replace(/[ \t]+$/gm, ''); // Remove trailing spaces

  return markdown;
}

/**
 * Process HTML lists and convert to markdown with proper nesting
 */
function processLists(html: string): string {
  // This is a simplified version - in a real implementation, you'd want
  // to properly parse the HTML tree to handle complex nesting
  let result = html;

  // Handle unordered lists
  result = result.replace(/<ul[^>]*>(.*?)<\/ul>/gs, (match, content) => {
    const items = extractListItems(content);
    return items.map(item => `- ${item}`).join('\n') + '\n';
  });

  // Handle ordered lists
  result = result.replace(/<ol[^>]*>(.*?)<\/ol>/gs, (match, content) => {
    const items = extractListItems(content);
    return items.map((item, index) => `${index + 1}. ${item}`).join('\n') + '\n';
  });

  return result;
}

/**
 * Extract list items from HTML list content
 */
function extractListItems(listContent: string): string[] {
  const items: string[] = [];
  const itemRegex = /<li[^>]*>(.*?)<\/li>/gs;
  let match;

  while ((match = itemRegex.exec(listContent)) !== null) {
    let itemContent = match[1].trim();
    // Remove any nested HTML tags for now (simplified)
    itemContent = itemContent.replace(/<[^>]+>/g, '');
    if (itemContent) {
      items.push(itemContent);
    }
  }

  return items;
}

/**
 * Escape HTML characters
 */
function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Check if text contains markdown syntax
 */
export function isMarkdown(text: string): boolean {
  const markdownPatterns = [
    /^#{1,6}\s+/, // Headers
    /^\s*[*\-+]\s+/, // Unordered lists
    /^\s*\d+\.\s+/, // Ordered lists
    /\*\*.*?\*\*/, // Bold
    /\*.*?\*/, // Italic
    /`.*?`/, // Inline code
    /```/, // Code blocks
    /\[.*?\]\(.*?\)/, // Links
    /~~.*?~~/, // Strikethrough
  ];

  return markdownPatterns.some(pattern => pattern.test(text));
}

/**
 * Clean and normalize markdown for AI tool compatibility
 */
export function normalizeMarkdown(markdown: string): string {
  return markdown
    // Normalize line endings
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n')
    // Remove excessive whitespace
    .replace(/[ \t]+$/gm, '') // Remove trailing spaces
    .replace(/\n{3,}/g, '\n\n') // Max 2 consecutive newlines
    // Normalize list formatting
    .replace(/^\s*[\*\+]\s+/gm, '- ') // Standardize bullet points to -
    .replace(/^\s*(\d+)\.\s+/gm, '$1. ') // Normalize numbered lists
    // Ensure proper spacing around headers
    .replace(/^(#{1,6})\s*(.+)$/gm, '$1 $2')
    // Clean up and normalize
    .trim();
}

/**
 * Convert HTML from external sources to clean markdown
 */
export function htmlFromExternalToMarkdown(html: string): string {
  // Remove common unwanted elements
  let cleaned = html
    // Remove script and style tags completely
    .replace(/<script[^>]*>.*?<\/script>/gis, '')
    .replace(/<style[^>]*>.*?<\/style>/gis, '')
    // Remove comments
    .replace(/<!--.*?-->/gs, '')
    // Remove meta tags and other head elements
    .replace(/<meta[^>]*>/gi, '')
    .replace(/<link[^>]*>/gi, '')
    // Clean up common formatting
    .replace(/<div[^>]*>/gi, '<p>')
    .replace(/<\/div>/gi, '</p>')
    .replace(/<span[^>]*>/gi, '')
    .replace(/<\/span>/gi, '')
    // Remove class and style attributes
    .replace(/\s+class="[^"]*"/gi, '')
    .replace(/\s+style="[^"]*"/gi, '')
    .replace(/\s+id="[^"]*"/gi, '');

  // Convert to markdown using our standard function
  const markdown = htmlToMarkdown(cleaned);

  // Normalize the result
  return normalizeMarkdown(markdown);
}

/**
 * Extract plain text from HTML while preserving basic structure
 */
export function htmlToPlainText(html: string): string {
  // Create a temporary div to parse HTML
  const div = document.createElement('div');
  div.innerHTML = html;

  // Remove script and style elements
  const scripts = div.querySelectorAll('script, style');
  scripts.forEach(el => el.remove());

  // Get text content and preserve line breaks
  let text = div.textContent || div.innerText || '';

  // Clean up excessive whitespace
  text = text
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Max 2 consecutive newlines
    .replace(/[ \t]+/g, ' ') // Normalize spaces
    .trim();

  return text;
}
