/**
 * Custom Tiptap extensions for enhanced markdown editing
 */

import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';

/**
 * Auto-formatting extension that converts markdown syntax as you type
 */
export const AutoFormat = Extension.create({
  name: 'autoFormat',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('autoFormat'),
        props: {
          handleTextInput(view, from, to, text) {
            const { state, dispatch } = view;
            const { tr } = state;
            
            // Get the text before the cursor
            const textBefore = state.doc.textBetween(Math.max(0, from - 20), from, '\n', ' ');
            const fullText = textBefore + text;
            
            // Auto-format bold: **text** -> bold
            const boldMatch = fullText.match(/\*\*([^*]+)\*\*$/);
            if (boldMatch && text === '*') {
              const matchStart = from - boldMatch[1].length - 3; // -3 for the first **
              const matchEnd = to;
              
              tr.delete(matchStart, matchEnd);
              tr.insertText(boldMatch[1], matchStart);
              tr.addMark(
                matchStart,
                matchStart + boldMatch[1].length,
                state.schema.marks.bold.create()
              );
              
              dispatch(tr);
              return true;
            }
            
            // Auto-format italic: *text* -> italic (but not if it's part of **)
            const italicMatch = fullText.match(/\*([^*]+)\*$/);
            if (italicMatch && text === '*' && !fullText.includes('**') && !fullText.endsWith('***')) {
              const matchStart = from - italicMatch[1].length - 1; // -1 for the first *
              const matchEnd = to;
              
              tr.delete(matchStart, matchEnd);
              tr.insertText(italicMatch[1], matchStart);
              tr.addMark(
                matchStart,
                matchStart + italicMatch[1].length,
                state.schema.marks.em.create()
              );
              
              dispatch(tr);
              return true;
            }
            
            // Auto-format inline code: `text` -> code
            const codeMatch = fullText.match(/`([^`]+)`$/);
            if (codeMatch && text === '`') {
              const matchStart = from - codeMatch[1].length - 1; // -1 for the first `
              const matchEnd = to;
              
              tr.delete(matchStart, matchEnd);
              tr.insertText(codeMatch[1], matchStart);
              tr.addMark(
                matchStart,
                matchStart + codeMatch[1].length,
                state.schema.marks.code.create()
              );
              
              dispatch(tr);
              return true;
            }
            
            // Auto-format strikethrough: ~~text~~ -> strikethrough
            const strikeMatch = fullText.match(/~~([^~]+)~~$/);
            if (strikeMatch && text === '~') {
              const matchStart = from - strikeMatch[1].length - 3; // -3 for the first ~~
              const matchEnd = to;
              
              tr.delete(matchStart, matchEnd);
              tr.insertText(strikeMatch[1], matchStart);
              tr.addMark(
                matchStart,
                matchStart + strikeMatch[1].length,
                state.schema.marks.strike.create()
              );
              
              dispatch(tr);
              return true;
            }
            
            return false;
          },
          
          handleKeyDown(view, event) {
            const { state, dispatch } = view;
            const { selection, tr } = state;
            
            // Auto-format headers when pressing space after #
            if (event.key === ' ') {
              const { $from } = selection;
              const textBefore = $from.parent.textContent.slice(0, $from.parentOffset);
              
              // Check for header patterns
              const headerMatch = textBefore.match(/^(#{1,6})$/);
              if (headerMatch) {
                const level = headerMatch[1].length;
                const start = $from.pos - headerMatch[1].length;
                
                tr.delete(start, $from.pos);
                tr.setBlockType(start, start, state.schema.nodes.heading, { level });
                
                dispatch(tr);
                return true;
              }
              
              // Auto-format lists when pressing space after - or 1.
              const listMatch = textBefore.match(/^(\s*)([-*+]|\d+\.)$/);
              if (listMatch) {
                const isOrdered = /^\d+\./.test(listMatch[2]);
                const listType = isOrdered ? 'orderedList' : 'bulletList';
                const start = $from.pos - listMatch[0].length;
                
                tr.delete(start, $from.pos);
                tr.setBlockType(start, start, state.schema.nodes[listType]);
                
                dispatch(tr);
                return true;
              }
            }
            
            // Auto-format code blocks when pressing Enter after ```
            if (event.key === 'Enter') {
              const { $from } = selection;
              const textBefore = $from.parent.textContent.slice(0, $from.parentOffset);
              
              if (textBefore === '```') {
                const start = $from.pos - 3;
                
                tr.delete(start, $from.pos);
                tr.setBlockType(start, start, state.schema.nodes.codeBlock);
                
                dispatch(tr);
                return true;
              }
            }
            
            return false;
          },
        },
      }),
    ];
  },
});

/**
 * Enhanced keyboard shortcuts extension
 */
export const EnhancedKeyboardShortcuts = Extension.create({
  name: 'enhancedKeyboardShortcuts',

  addKeyboardShortcuts() {
    return {
      // Text formatting
      'Mod-b': () => this.editor.commands.toggleBold(),
      'Mod-i': () => this.editor.commands.toggleItalic(),
      'Mod-u': () => this.editor.commands.toggleStrike(), // Using Mod-u for strikethrough
      'Mod-e': () => this.editor.commands.toggleCode(),
      'Mod-k': () => {
        // Toggle link
        if (this.editor.isActive('link')) {
          this.editor.commands.unsetLink();
        } else {
          const url = window.prompt('Enter URL:');
          if (url) {
            this.editor.commands.setLink({ href: url });
          }
        }
        return true;
      },
      
      // Headers
      'Mod-Alt-1': () => this.editor.commands.toggleHeading({ level: 1 }),
      'Mod-Alt-2': () => this.editor.commands.toggleHeading({ level: 2 }),
      'Mod-Alt-3': () => this.editor.commands.toggleHeading({ level: 3 }),
      'Mod-Alt-4': () => this.editor.commands.toggleHeading({ level: 4 }),
      'Mod-Alt-5': () => this.editor.commands.toggleHeading({ level: 5 }),
      'Mod-Alt-6': () => this.editor.commands.toggleHeading({ level: 6 }),
      'Mod-Alt-0': () => this.editor.commands.setParagraph(),
      
      // Lists
      'Mod-Shift-8': () => this.editor.commands.toggleBulletList(),
      'Mod-Shift-7': () => this.editor.commands.toggleOrderedList(),
      
      // Code block
      'Mod-Alt-c': () => this.editor.commands.toggleCodeBlock(),
      
      // List indentation
      'Tab': () => {
        if (this.editor.isActive('listItem')) {
          return this.editor.commands.sinkListItem('listItem');
        }
        return false;
      },
      'Shift-Tab': () => {
        if (this.editor.isActive('listItem')) {
          return this.editor.commands.liftListItem('listItem');
        }
        return false;
      },
      
      // Navigation and selection
      'Mod-a': () => this.editor.commands.selectAll(),
      'Mod-z': () => this.editor.commands.undo(),
      'Mod-Shift-z': () => this.editor.commands.redo(),
      'Mod-y': () => this.editor.commands.redo(),
      
      // Line breaks
      'Shift-Enter': () => this.editor.commands.setHardBreak(),
      
      // Clear formatting
      'Mod-\\': () => this.editor.commands.clearNodes().unsetAllMarks(),
    };
  },
});

/**
 * Smart paste extension for better handling of pasted content
 */
export const SmartPaste = Extension.create({
  name: 'smartPaste',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('smartPaste'),
        props: {
          handlePaste(view, event, slice) {
            const { state, dispatch } = view;

            // Get the pasted content
            const clipboardData = event.clipboardData;
            if (!clipboardData) return false;

            const htmlContent = clipboardData.getData('text/html');
            const textContent = clipboardData.getData('text/plain');

            // Handle HTML content from external sources
            if (htmlContent && htmlContent.trim()) {
              // Check if it's from an external source (contains complex HTML)
              const isExternalHtml = htmlContent.includes('<meta') ||
                                   htmlContent.includes('<style') ||
                                   htmlContent.includes('class=') ||
                                   htmlContent.includes('style=');

              if (isExternalHtml) {
                event.preventDefault();

                // Import the utility function dynamically to avoid circular dependencies
                import('@/lib/markdown-utils').then(({ htmlFromExternalToMarkdown, markdownToHtml }) => {
                  const markdown = htmlFromExternalToMarkdown(htmlContent);
                  const cleanHtml = markdownToHtml(markdown);

                  // Insert the cleaned content
                  const { from } = state.selection;
                  const tr = state.tr;

                  // Parse the HTML and insert it
                  const parser = new DOMParser();
                  const doc = parser.parseFromString(cleanHtml, 'text/html');
                  const content = doc.body.innerHTML;

                  tr.insertText(markdown, from);
                  dispatch(tr);
                });

                return true;
              }

              // For simple HTML, let Tiptap handle it normally
              return false;
            }

            // Handle plain text that looks like markdown
            if (textContent && isMarkdownLike(textContent)) {
              event.preventDefault();

              // Import the utility function dynamically
              import('@/lib/markdown-utils').then(({ normalizeMarkdown }) => {
                const cleanMarkdown = normalizeMarkdown(textContent);
                const { from } = state.selection;
                const tr = state.tr;

                tr.insertText(cleanMarkdown, from);
                dispatch(tr);
              });

              return true;
            }

            return false;
          },

          // Handle drag and drop
          handleDrop(view, event, slice, moved) {
            const files = Array.from(event.dataTransfer?.files || []);

            // Handle text files
            const textFiles = files.filter(file => file.type.startsWith('text/'));
            if (textFiles.length > 0) {
              event.preventDefault();

              textFiles.forEach(file => {
                const reader = new FileReader();
                reader.onload = (e) => {
                  const content = e.target?.result as string;
                  if (content) {
                    import('@/lib/markdown-utils').then(({ normalizeMarkdown, isMarkdown }) => {
                      const processedContent = isMarkdown(content)
                        ? normalizeMarkdown(content)
                        : content;

                      const { from } = view.state.selection;
                      const tr = view.state.tr;
                      tr.insertText(processedContent, from);
                      view.dispatch(tr);
                    });
                  }
                };
                reader.readAsText(file);
              });

              return true;
            }

            return false;
          },
        },
      }),
    ];
  },
});

/**
 * Check if text looks like markdown
 */
function isMarkdownLike(text: string): boolean {
  const markdownPatterns = [
    /^#{1,6}\s+/, // Headers
    /^\s*[*\-+]\s+/, // Unordered lists
    /^\s*\d+\.\s+/, // Ordered lists
    /\*\*.*?\*\*/, // Bold
    /\*.*?\*/, // Italic
    /`.*?`/, // Inline code
    /```/, // Code blocks
    /\[.*?\]\(.*?\)/, // Links
    /~~.*?~~/, // Strikethrough
  ];

  return markdownPatterns.some(pattern => pattern.test(text));
}
