# Date and Time Picker Standardization

This document outlines the standardization of date and time pickers across the application to ensure consistent user experience, accessibility, and styling.

## Components

### DatePicker Component

The main `DatePicker` component is located at `src/components/ui/date-picker.tsx`. This component provides a standardized way to select dates across the application.

### DateTimePicker Component

The `DateTimePicker` component is located at `src/components/ui/datetime-picker.tsx`. This component extends the DatePicker functionality to include time selection capabilities.

#### DatePicker Features

- Built with shadcn/ui components for consistent styling
- Accessible by default with proper ARIA attributes
- Keyboard navigation support
- Screen reader friendly
- Clean interface with calendar popover for date selection
- Selected dates display as pill/badge with integrated clear button
- Today button in calendar popover for easy date selection
- Responsive design
- Support for disabled state
- Error state handling
- No visible labels (screen reader accessible labels only)

#### DateTimePicker Features

- All DatePicker features plus:
- **Separate pill layout**: Date and time displayed as distinct interactive elements
- **Date pill**: Primary pill showing the date with X clear button
- **Time pill**: Secondary smaller pill below date pill showing time with X clear button
- **Vertical stacking**: Time pill positioned directly underneath date pill
- **Independent interaction**: Each pill clickable independently to edit date vs time
- **Add time placeholder**: "Add time..." pill when date exists but no time is set
- **Custom time picker**: Uses shadcn/ui Select components for consistent visual design
- **5-minute increments**: Minute selection limited to 5-minute intervals (00, 05, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55)
- **12-hour format**: Hour selection from 1-12 with AM/PM toggle for intuitive time entry
- **Proper z-index layering**: Select dropdowns appear above all other UI elements
- **Optimized dropdown height**: Maximum height of 200px with internal scrolling for better UX
- Independent time clearing functionality (separate from date clearing)
- Graceful handling of dates with and without time components
- Automatic time formatting and display
- Backward compatibility with date-only tasks

#### Usage

**DatePicker (date only):**
```tsx
import { DatePicker } from "@/components/ui/date-picker";
import { useState } from "react";

export default function MyComponent() {
  const [date, setDate] = useState<Date | undefined>(undefined);

  return (
    <DatePicker
      date={date}
      setDate={setDate}
      placeholder="Select a date"
    />
  );
}
```

**DateTimePicker (date and time):**
```tsx
import { DateTimePicker } from "@/components/ui/datetime-picker";
import { useState } from "react";

export default function MyComponent() {
  const [date, setDate] = useState<Date | undefined>(undefined);

  return (
    <DateTimePicker
      date={date}
      setDate={setDate}
      placeholder="Select due date"
      includeTime={true}
    />
  );
}
```

### Date Utilities

A set of utility functions for consistent date formatting and manipulation is provided in `src/lib/date-utils.ts`.

#### Functions

**Date Formatting:**
- `formatDate(date, formatStr)`: Format a date with a consistent format across the application
- `formatDateTime(date, includeTime)`: Format a datetime with both date and time
- `formatTime(date)`: Format time only from a date
- `getRelativeDateString(date, includeTime)`: Get a human-readable relative date string (Today, Tomorrow, etc.)

**Date Manipulation:**
- `parseDate(dateStr, formatStr)`: Parse a date string into a Date object
- `setTimeOnDate(date, hours, minutes)`: Set time on a date object
- `clearTimeFromDate(date)`: Clear time from a date (set to midnight)

**Date Validation:**
- `isValidDate(date)`: Check if a date is valid
- `hasTimeComponent(date)`: Check if a date has a time component (not midnight)

**Date Utilities:**
- `getStartOfToday()`: Get the start of today
- `getTomorrow()`: Get tomorrow's date
- `isPastDue(date)`: Check if a date is past due (before today)

#### Usage

```tsx
import {
  formatDate,
  formatDateTime,
  formatTime,
  getRelativeDateString,
  hasTimeComponent,
  setTimeOnDate,
  clearTimeFromDate
} from "@/lib/date-utils";

// Format a date
const formattedDate = formatDate(new Date(), "MMM d, yyyy"); // "Jan 1, 2023"

// Format a datetime
const dateWithTime = new Date('2024-01-15T14:30:00');
const formattedDateTime = formatDateTime(dateWithTime, true); // "Jan 15, 2024 at 2:30 PM"

// Get a relative date string with time
const relativeDate = getRelativeDateString(dateWithTime, true); // "Today at 2:30 PM"

// Check if date has time component
const hasTime = hasTimeComponent(dateWithTime); // true

// Set time on a date
const dateOnly = new Date('2024-01-15T00:00:00');
const withTime = setTimeOnDate(dateOnly, 9, 30); // Sets to 9:30 AM

// Clear time from a date
const clearedTime = clearTimeFromDate(dateWithTime); // Sets to midnight
```

## Implementation Details

### TimePicker Component (`src/components/ui/time-picker.tsx`)

A custom time picker component built with shadcn/ui Select components that provides:
- **Hour Selection**: Dropdown with options 1-12 (12-hour format by default) with AM/PM toggle
- **Minute Selection**: Dropdown with 5-minute increment options (00, 05, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55)
- **Z-index Optimization**: Select dropdowns use `z-[100]` to appear above all other UI elements
- **Dropdown Height Control**: Maximum height of 200px with internal scrolling for better UX
- **Consistent Styling**: Matches shadcn/ui design system and calendar picker styling
- **Accessibility**: Full keyboard navigation and screen reader support
- **Time Parsing**: Proper HH:MM format parsing and validation with 12-hour to 24-hour conversion

### Components Using DateTimePicker

The following components use the standardized DateTimePicker component:

1. `src/app/(protected)/tasks/components/add-task-dialog.tsx`
2. `src/app/(protected)/tasks/components/edit-task-dialog.tsx`
3. `src/app/(protected)/tasks/components/task-item.tsx`

### Components Using DatePicker (Date Only)

The DatePicker component is available for use cases that only require date selection without time.

### Components Using Date Utilities

The following components use the date utility functions for consistent date formatting:

1. `src/app/(protected)/calendar/components/calendar-view.tsx`
2. `src/app/(protected)/dashboard/components/due-date-summary.tsx`
3. `src/app/(protected)/tasks/components/task-item.tsx`

## Interaction Patterns

### DateTimePicker Interaction Flow

1. **Initial State**: Shows date input field placeholder
2. **Date Selected**: Date pill appears with calendar icon and clear (X) button
3. **Time Available**: When `includeTime={true}` and date is selected:
   - If no time set: Shows "Add time..." placeholder pill below date pill
   - If time set: Shows time pill with clock icon and time value
4. **Date Pill Click**: Opens calendar popover for date selection
5. **Time Pill Click**: Opens time input popover for time selection
6. **Add Time Click**: Opens time input popover and sets default time when value entered
7. **Clear Actions**:
   - Date pill X button: Clears entire date (and time)
   - Time pill X button: Clears only time, preserves date
8. **Time Input Behavior**:
   - **Custom Time Picker**: Uses shadcn/ui Select dropdowns for hour, minute, and AM/PM selection
   - **Hour Selection**: Dropdown with options 1-12 (12-hour format) for intuitive time entry
   - **Minute Selection**: Dropdown with 5-minute increment options (00, 05, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55)
   - **AM/PM Toggle**: Separate dropdown for period selection with proper time conversion
   - **Z-index Layering**: Dropdowns appear above time picker popover and other UI elements
   - **Optimized Scrolling**: Maximum dropdown height of 200px with internal scrolling
   - **Visual Consistency**: Matches calendar picker styling and theme
   - **Accessibility**: Full keyboard navigation and screen reader support

### Visual Hierarchy

- **Date pill**: Primary element with full secondary background styling
- **Time pill**: Secondary element with muted background, smaller size
- **Spacing**: 8px vertical gap between date and time pills
- **Alignment**: Time pill indented 8px from left edge of date pill

## Accessibility

The standardized DatePicker and DateTimePicker components follow accessibility best practices:

- Proper ARIA attributes for screen readers
- Keyboard navigation support
- Focus management
- High contrast mode support
- Clear visual feedback for selected dates
- Error states with descriptive messages

### Keyboard Navigation

- `Tab`: Move focus to the date picker button
- `Enter` or `Space`: Open the date picker popover
- `Escape`: Close the date picker popover
- `Arrow keys`: Navigate between days in the calendar
- `Home`: Go to the first day of the month
- `End`: Go to the last day of the month
- `Page Up`: Go to the previous month
- `Page Down`: Go to the next month
- `Enter` or `Space`: Select the focused day

## Styling

The DatePicker component uses shadcn/ui components for consistent styling across the application. The styling follows the shadcn/ui design system and integrates seamlessly with other shadcn/ui components.

## Testing

A test file for the DatePicker component is provided at `src/components/ui/__tests__/date-picker.test.tsx`. This file contains tests for the component's functionality and accessibility.

A manual test script is also provided at `src/components/ui/date-picker.test.js`. This script can be run in the browser console to test the DatePicker component's functionality.

## Future Improvements

- Add support for date ranges
- Add support for different date formats
- Add support for localization
- Add timezone selection capabilities
- Add recurring date/time patterns
- Add date/time validation rules
